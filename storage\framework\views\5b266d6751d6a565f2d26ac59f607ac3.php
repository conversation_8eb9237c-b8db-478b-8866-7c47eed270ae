<?php echo $__env->make(EcommerceHelper::viewPath('includes.product-price'), [
    'priceWrapperClassName' => 'tp-product-price-wrapper',
    'priceClassName' => 'tp-product-price new-price',
    'priceOriginalWrapperClassName' => '',
    'priceOriginalClassName' => 'tp-product-price old-price',
], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php /**PATH D:\hassan code\shofy\platform\themes/shofy/views/ecommerce/includes/product/style-1/price.blade.php ENDPATH**/ ?>