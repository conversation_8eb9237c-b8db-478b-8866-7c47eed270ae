<?php if (! $__env->hasRenderedOnce('eae068b0-2a7e-43d7-b4ba-3a69a0a736a8')): $__env->markAsRenderedOnce('eae068b0-2a7e-43d7-b4ba-3a69a0a736a8'); ?>
    <script>
        var lazyLoadShortcodeBlocks = function () {
            $('.shortcode-lazy-loading').each(function (index, element) {
                var $element = $(element);
                var name = $element.data('name');
                var attributes = $element.data('attributes');

                $.ajax({
                    url: '<?php echo e(route('public.ajax.render-ui-block')); ?>',
                    type: 'POST',
                    data: {
                        name,
                        attributes: {
                            ...attributes,
                        },
                    },
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    success: function ({ error, data }) {
                        if (error) {
                            return;
                        }

                        $element.replaceWith(data);

                        document.dispatchEvent(new CustomEvent('shortcode.loaded', {
                            detail: {
                                name,
                                attributes,
                                html: data,
                            }
                        }));
                    },
                });
            });
        };

        window.addEventListener('load', function () {
            lazyLoadShortcodeBlocks();
        });
    </script>
<?php endif; ?>
<?php /**PATH D:\hassan code\shofy\platform/packages/shortcode/resources/views/partials/lazy-loading-script.blade.php ENDPATH**/ ?>