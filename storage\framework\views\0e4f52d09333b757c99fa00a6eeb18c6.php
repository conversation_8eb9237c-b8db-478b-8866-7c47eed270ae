<?php if (! $__env->hasRenderedOnce('b820fd08-2b20-4df1-90aa-1a9e2c2480a3')): $__env->markAsRenderedOnce('b820fd08-2b20-4df1-90aa-1a9e2c2480a3'); ?>
    <script>
        var lazyLoadShortcodeBlocks = function () {
            $('.shortcode-lazy-loading').each(function (index, element) {
                var $element = $(element);
                var name = $element.data('name');
                var attributes = $element.data('attributes');

                $.ajax({
                    url: '<?php echo e(route('public.ajax.render-ui-block')); ?>',
                    type: 'POST',
                    data: {
                        name,
                        attributes: {
                            ...attributes,
                        },
                    },
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    success: function ({ error, data }) {
                        if (error) {
                            return;
                        }

                        $element.replaceWith(data);

                        document.dispatchEvent(new CustomEvent('shortcode.loaded', {
                            detail: {
                                name,
                                attributes,
                                html: data,
                            }
                        }));
                    },
                });
            });
        };

        window.addEventListener('load', function () {
            lazyLoadShortcodeBlocks();
        });
    </script>
<?php endif; ?>
<?php /**PATH D:\hassan code\shofy\platform/packages/shortcode/resources/views/partials/lazy-loading-script.blade.php ENDPATH**/ ?>