[2025-11-29 16:24:09] production.ERROR: SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo for mysql failed: No such host is known.  (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'shofy' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo for mysql failed: No such host is known.  (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'shofy' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#1 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#2 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#3 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#4 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#5 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#6 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#7 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#8 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(700): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#9 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#10 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(248): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#11 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#12 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#13 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#14 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#15 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#16 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#17 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 D:\\hassan code\\shofy\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] php_network_getaddresses: getaddrinfo for mysql failed: No such host is known.  at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=mysq...', 'sail', Object(SensitiveParameterValue), Array)
#1 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=mysq...', 'sail', 'password', Array)
#2 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=mysq...', 'sail', 'password', Array)
#3 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=mysq...', Array, Array)
#4 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#7 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#8 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select table_na...', Array)
#10 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#11 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#12 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#13 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#14 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#15 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#16 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#17 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#18 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(700): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#19 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#20 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(248): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#21 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#22 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#23 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#24 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#25 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#26 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#27 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#32 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 D:\\hassan code\\shofy\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 {main}

[previous exception] [object] (PDOException(code: 0): PDO::__construct(): php_network_getaddresses: getaddrinfo for mysql failed: No such host is known.  at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=mysq...', 'sail', Object(SensitiveParameterValue), Array)
#1 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=mysq...', 'sail', 'password', Array)
#2 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=mysq...', 'sail', 'password', Array)
#3 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=mysq...', Array, Array)
#4 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#7 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#8 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select table_na...', Array)
#10 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#11 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#12 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#13 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#14 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#15 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#16 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#17 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#18 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(700): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#19 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#20 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(248): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#21 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#22 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#23 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#24 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#25 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#26 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#27 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#32 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 D:\\hassan code\\shofy\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 D:\\hassan code\\shofy\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 {main}
"} 
[2025-11-29 16:32:39] production.ERROR: Driver [fileyes] not supported. {"exception":"[object] (InvalidArgumentException(code: 0): Driver [fileyes] not supported. at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php:109)
[stacktrace]
#0 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php(80): Illuminate\\Support\\Manager->createDriver('fileyes')
#1 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php(52): Illuminate\\Support\\Manager->driver()
#2 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Session\\SessionServiceProvider->Illuminate\\Session\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#3 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#4 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(961): Illuminate\\Container\\Container->resolve('session.store', Array, true)
#5 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('session.store', Array)
#6 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(946): Illuminate\\Container\\Container->make('session.store', Array)
#7 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('session.store')
#8 D:\\hassan code\\shofy\\vendor\\botble\\form-builder\\src\\FormBuilderServiceProvider.php(33): Illuminate\\Container\\Container->offsetGet('session.store')
#9 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Kris\\LaravelFormBuilder\\FormBuilderServiceProvider->Kris\\LaravelFormBuilder\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#10 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#11 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(961): Illuminate\\Container\\Container->resolve('form', Array, true)
#12 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('form', Array)
#13 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(946): Illuminate\\Container\\Container->make('form', Array)
#14 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('form')
#15 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('form')
#16 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('form')
#17 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(349): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#18 D:\\hassan code\\shofy\\platform\\packages\\slug\\src\\Providers\\FormServiceProvider.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('component', Array)
#19 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1062): Botble\\Slug\\Providers\\FormServiceProvider->Botble\\Slug\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#20 D:\\hassan code\\shofy\\platform\\packages\\slug\\src\\Providers\\FormServiceProvider.php(12): Illuminate\\Foundation\\Application->booted(Object(Closure))
#21 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Botble\\Slug\\Providers\\FormServiceProvider->boot()
#22 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Container\\Container->call(Array)
#27 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Illuminate\\Foundation\\Application->bootProvider(Object(Botble\\Slug\\Providers\\FormServiceProvider))
#28 D:\\hassan code\\shofy\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php(68): Illuminate\\Foundation\\Application->register(Object(Botble\\Slug\\Providers\\FormServiceProvider))
#29 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Botble\\Slug\\Providers\\SlugServiceProvider->Botble\\Slug\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#30 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1020): Illuminate\\Foundation\\Application->fireAppCallbacks(Array)
#31 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#32 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#33 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#34 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#35 D:\\hassan code\\shofy\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-11-29 16:48:51] production.ERROR: Driver [fileyes] not supported. {"exception":"[object] (InvalidArgumentException(code: 0): Driver [fileyes] not supported. at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php:109)
[stacktrace]
#0 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Manager.php(80): Illuminate\\Support\\Manager->createDriver('fileyes')
#1 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionServiceProvider.php(52): Illuminate\\Support\\Manager->driver()
#2 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Session\\SessionServiceProvider->Illuminate\\Session\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#3 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#4 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(961): Illuminate\\Container\\Container->resolve('session.store', Array, true)
#5 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('session.store', Array)
#6 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(946): Illuminate\\Container\\Container->make('session.store', Array)
#7 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('session.store')
#8 D:\\hassan code\\shofy\\vendor\\botble\\form-builder\\src\\FormBuilderServiceProvider.php(33): Illuminate\\Container\\Container->offsetGet('session.store')
#9 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Kris\\LaravelFormBuilder\\FormBuilderServiceProvider->Kris\\LaravelFormBuilder\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#10 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#11 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(961): Illuminate\\Container\\Container->resolve('form', Array, true)
#12 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('form', Array)
#13 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(946): Illuminate\\Container\\Container->make('form', Array)
#14 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('form')
#15 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('form')
#16 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('form')
#17 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(349): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#18 D:\\hassan code\\shofy\\platform\\packages\\slug\\src\\Providers\\FormServiceProvider.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('component', Array)
#19 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1062): Botble\\Slug\\Providers\\FormServiceProvider->Botble\\Slug\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#20 D:\\hassan code\\shofy\\platform\\packages\\slug\\src\\Providers\\FormServiceProvider.php(12): Illuminate\\Foundation\\Application->booted(Object(Closure))
#21 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Botble\\Slug\\Providers\\FormServiceProvider->boot()
#22 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1034): Illuminate\\Container\\Container->call(Array)
#27 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Illuminate\\Foundation\\Application->bootProvider(Object(Botble\\Slug\\Providers\\FormServiceProvider))
#28 D:\\hassan code\\shofy\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php(68): Illuminate\\Foundation\\Application->register(Object(Botble\\Slug\\Providers\\FormServiceProvider))
#29 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Botble\\Slug\\Providers\\SlugServiceProvider->Botble\\Slug\\Providers\\{closure}(Object(Illuminate\\Foundation\\Application))
#30 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1020): Illuminate\\Foundation\\Application->fireAppCallbacks(Array)
#31 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#32 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(263): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#33 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#34 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#35 D:\\hassan code\\shofy\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-11-29 17:00:43] production.ERROR: Maximum execution time of 300 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 300 seconds exceeded at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:95)
[stacktrace]
#0 {main}
"} 
[2025-11-29 17:06:41] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'shofy.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (a0797a1c-7082-457c-862a-adca1257512e, {"connection":"mysql","driver":"mysql","bindings":[],"sql":"select * from `languages` order by `lang_order` asc","time":"0.68","slow":false,"file":"D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php","line":48,"hash":"b46aa2b5b43866595c382f2af177e0c6","hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:06:41, ?, query, a0797a1b-d31f-46d9-865c-d0e13aee3afb), (a0797a1c-7082-457c-862a-adca1257512e, {"action":"retrieved","model":"Botble\\Language\\Models\\Language","count":1,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:06:41, ?, model, a0797a1b-d600-4e94-8eef-996c8a476609), (a0797a1c-7082-457c-862a-adca1257512e, {"name":"Kris\\LaravelFormBuilder\\Events\\FormComponentRegistering","payload":{"form":{"class":"Kris\\LaravelFormBuilder\\Supports\\FormBuilder","properties":[]}},"listeners":[{"name":"Closure at D:\\hassan code\\shofy\\platform\\core\\base\\src\\Providers\\FormServiceProvider.php[41:252]","queued":false}],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:06:41, ?, event, a0797a1b-fc4b-42d5-85ea-6f7fdd2a8193), (a0797a1c-7082-457c-862a-adca1257512e, {"name":"Kris\\LaravelFormBuilder\\Events\\FormComponentRegistered","payload":{"form":{"class":"Kris\\LaravelFormBuilder\\Supports\\FormBuilder","properties":[]}},"listeners":[],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:06:41, ?, event, a0797a1b-fca9-4171-89ab-f8f3759755a9), (a0797a1c-7082-457c-862a-adca1257512e, {"name":"Botble\\Theme\\Events\\ThemeRoutingBeforeEvent","payload":{"router":{"class":"Illuminate\\Routing\\Router","properties":{"middlewarePriority":["Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful","Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests","Illuminate\\Cookie\\Middleware\\EncryptCookies","Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse","Illuminate\\Session\\Middleware\\StartSession","Illuminate\\View\\Middleware\\ShareErrorsFromSession","Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests","Illuminate\\Routing\\Middleware\\ThrottleRequests","Illuminate\\Routing\\Middleware\\ThrottleRequestsWithRedis","Illuminate\\Contracts\\Session\\Middleware\\AuthenticatesSessions","Illuminate\\Routing\\Middleware\\SubstituteBindings","Illuminate\\Auth\\Middleware\\Authorize"]}}},"listeners":[{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\blog\\src\\Providers\\BlogServiceProvider.php[76:82]","queued":false},{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\routes\\base.php[209:249]","queued":false},{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Providers\\EcommerceServiceProvider.php[427:434]","queued":false},{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\gallery\\src\\Providers\\GalleryServiceProvider.php[54:56]","queued":false}],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:06:41, ?, event, a0797a1c-179b-4b87-a42c-edf646a5971d), (a0797a1c-7082-457c-862a-adca1257512e, {"name":"Botble\\Theme\\Events\\ThemeRoutingAfterEvent","payload":{"router":{"class":"Illuminate\\Routing\\Router","properties":{"middlewarePriority":["Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful","Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests","Illuminate\\Cookie\\Middleware\\EncryptCookies","Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse","Illuminate\\Session\\Middleware\\StartSession","Illuminate\\View\\Middleware\\ShareErrorsFromSession","Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests","Illuminate\\Routing\\Middleware\\ThrottleRequests","Illuminate\\Routing\\Middleware\\ThrottleRequestsWithRedis","Illuminate\\Contracts\\Session\\Middleware\\AuthenticatesSessions","Illuminate\\Routing\\Middleware\\SubstituteBindings","Illuminate\\Auth\\Middleware\\Authorize"]}}},"listeners":[],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:06:41, ?, event, a0797a1c-1805-42a6-8ab7-54869e9fdca7), (a0797a1c-7082-457c-862a-adca1257512e, {"command":"vendor:publish","exit_code":0,"arguments":{"command":"vendor:publish"},"options":{"existing":false,"force":true,"all":false,"provider":null,"tag":["laravel-assets"],"help":false,"quiet":false,"verbose":false,"version":false,"ansi":true,"no-interaction":false,"env":null},"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:06:41, ?, command, a0797a1c-6fbc-4796-96bd-63d99a0e0b1c)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'shofy.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (a0797a1c-7082-457c-862a-adca1257512e, {\"connection\":\"mysql\",\"driver\":\"mysql\",\"bindings\":[],\"sql\":\"select * from `languages` order by `lang_order` asc\",\"time\":\"0.68\",\"slow\":false,\"file\":\"D:\\\\hassan code\\\\shofy\\\\platform\\\\core\\\\base\\\\src\\\\Models\\\\BaseQueryBuilder.php\",\"line\":48,\"hash\":\"b46aa2b5b43866595c382f2af177e0c6\",\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:06:41, ?, query, a0797a1b-d31f-46d9-865c-d0e13aee3afb), (a0797a1c-7082-457c-862a-adca1257512e, {\"action\":\"retrieved\",\"model\":\"Botble\\\\Language\\\\Models\\\\Language\",\"count\":1,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:06:41, ?, model, a0797a1b-d600-4e94-8eef-996c8a476609), (a0797a1c-7082-457c-862a-adca1257512e, {\"name\":\"Kris\\\\LaravelFormBuilder\\\\Events\\\\FormComponentRegistering\",\"payload\":{\"form\":{\"class\":\"Kris\\\\LaravelFormBuilder\\\\Supports\\\\FormBuilder\",\"properties\":[]}},\"listeners\":[{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\core\\\\base\\\\src\\\\Providers\\\\FormServiceProvider.php[41:252]\",\"queued\":false}],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:06:41, ?, event, a0797a1b-fc4b-42d5-85ea-6f7fdd2a8193), (a0797a1c-7082-457c-862a-adca1257512e, {\"name\":\"Kris\\\\LaravelFormBuilder\\\\Events\\\\FormComponentRegistered\",\"payload\":{\"form\":{\"class\":\"Kris\\\\LaravelFormBuilder\\\\Supports\\\\FormBuilder\",\"properties\":[]}},\"listeners\":[],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:06:41, ?, event, a0797a1b-fca9-4171-89ab-f8f3759755a9), (a0797a1c-7082-457c-862a-adca1257512e, {\"name\":\"Botble\\\\Theme\\\\Events\\\\ThemeRoutingBeforeEvent\",\"payload\":{\"router\":{\"class\":\"Illuminate\\\\Routing\\\\Router\",\"properties\":{\"middlewarePriority\":[\"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\HandlePrecognitiveRequests\",\"Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies\",\"Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse\",\"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\"Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession\",\"Illuminate\\\\Contracts\\\\Auth\\\\Middleware\\\\AuthenticatesRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequestsWithRedis\",\"Illuminate\\\\Contracts\\\\Session\\\\Middleware\\\\AuthenticatesSessions\",\"Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings\",\"Illuminate\\\\Auth\\\\Middleware\\\\Authorize\"]}}},\"listeners\":[{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\blog\\\\src\\\\Providers\\\\BlogServiceProvider.php[76:82]\",\"queued\":false},{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\ecommerce\\\\routes\\\\base.php[209:249]\",\"queued\":false},{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\ecommerce\\\\src\\\\Providers\\\\EcommerceServiceProvider.php[427:434]\",\"queued\":false},{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\gallery\\\\src\\\\Providers\\\\GalleryServiceProvider.php[54:56]\",\"queued\":false}],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:06:41, ?, event, a0797a1c-179b-4b87-a42c-edf646a5971d), (a0797a1c-7082-457c-862a-adca1257512e, {\"name\":\"Botble\\\\Theme\\\\Events\\\\ThemeRoutingAfterEvent\",\"payload\":{\"router\":{\"class\":\"Illuminate\\\\Routing\\\\Router\",\"properties\":{\"middlewarePriority\":[\"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\HandlePrecognitiveRequests\",\"Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies\",\"Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse\",\"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\"Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession\",\"Illuminate\\\\Contracts\\\\Auth\\\\Middleware\\\\AuthenticatesRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequestsWithRedis\",\"Illuminate\\\\Contracts\\\\Session\\\\Middleware\\\\AuthenticatesSessions\",\"Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings\",\"Illuminate\\\\Auth\\\\Middleware\\\\Authorize\"]}}},\"listeners\":[],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:06:41, ?, event, a0797a1c-1805-42a6-8ab7-54869e9fdca7), (a0797a1c-7082-457c-862a-adca1257512e, {\"command\":\"vendor:publish\",\"exit_code\":0,\"arguments\":{\"command\":\"vendor:publish\"},\"options\":{\"existing\":false,\"force\":true,\"all\":false,\"provider\":null,\"tag\":[\"laravel-assets\"],\"help\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":true,\"no-interaction\":false,\"env\":null},\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:06:41, ?, command, a0797a1c-6fbc-4796-96bd-63d99a0e0b1c)) at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3498): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(664): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(289): call_user_func(Object(Closure))
#9 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(656): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1278): Illuminate\\Container\\Container->call(Object(Closure))
#17 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#18 D:\\hassan code\\shofy\\artisan(20): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'shofy.telescope_entries' doesn't exist at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:39)
[stacktrace]
#0 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(39): PDO->prepare('insert into `te...')
#1 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3498): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(664): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(289): call_user_func(Object(Closure))
#11 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(656): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1278): Illuminate\\Container\\Container->call(Object(Closure))
#19 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#20 D:\\hassan code\\shofy\\artisan(20): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 {main}
"} 
[2025-11-29 17:07:42] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'shofy.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (a0797a79-ac65-4c0d-b827-329b4e8d8011, {"connection":"mysql","driver":"mysql","bindings":[],"sql":"select * from `languages` order by `lang_order` asc","time":"0.88","slow":false,"file":"D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php","line":48,"hash":"b46aa2b5b43866595c382f2af177e0c6","hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:07:42, ?, query, a0797a78-e9f4-4d9e-8d6d-302b426bb89f), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {"action":"retrieved","model":"Botble\\Language\\Models\\Language","count":1,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:07:42, ?, model, a0797a78-ecfb-41b5-a71f-9e7a4274e56e), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {"name":"Kris\\LaravelFormBuilder\\Events\\FormComponentRegistering","payload":{"form":{"class":"Kris\\LaravelFormBuilder\\Supports\\FormBuilder","properties":[]}},"listeners":[{"name":"Closure at D:\\hassan code\\shofy\\platform\\core\\base\\src\\Providers\\FormServiceProvider.php[41:252]","queued":false}],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:07:42, ?, event, a0797a79-139f-4453-99f1-96a7f30b6648), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {"name":"Kris\\LaravelFormBuilder\\Events\\FormComponentRegistered","payload":{"form":{"class":"Kris\\LaravelFormBuilder\\Supports\\FormBuilder","properties":[]}},"listeners":[],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:07:42, ?, event, a0797a79-13fa-4c3e-b294-2ad034405fc8), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {"name":"Botble\\Theme\\Events\\ThemeRoutingBeforeEvent","payload":{"router":{"class":"Illuminate\\Routing\\Router","properties":{"middlewarePriority":["Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful","Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests","Illuminate\\Cookie\\Middleware\\EncryptCookies","Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse","Illuminate\\Session\\Middleware\\StartSession","Illuminate\\View\\Middleware\\ShareErrorsFromSession","Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests","Illuminate\\Routing\\Middleware\\ThrottleRequests","Illuminate\\Routing\\Middleware\\ThrottleRequestsWithRedis","Illuminate\\Contracts\\Session\\Middleware\\AuthenticatesSessions","Illuminate\\Routing\\Middleware\\SubstituteBindings","Illuminate\\Auth\\Middleware\\Authorize"]}}},"listeners":[{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\blog\\src\\Providers\\BlogServiceProvider.php[76:82]","queued":false},{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\routes\\base.php[209:249]","queued":false},{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Providers\\EcommerceServiceProvider.php[427:434]","queued":false},{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\gallery\\src\\Providers\\GalleryServiceProvider.php[54:56]","queued":false}],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:07:42, ?, event, a0797a79-2e7e-473d-9f54-dafc78768cce), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {"name":"Botble\\Theme\\Events\\ThemeRoutingAfterEvent","payload":{"router":{"class":"Illuminate\\Routing\\Router","properties":{"middlewarePriority":["Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful","Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests","Illuminate\\Cookie\\Middleware\\EncryptCookies","Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse","Illuminate\\Session\\Middleware\\StartSession","Illuminate\\View\\Middleware\\ShareErrorsFromSession","Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests","Illuminate\\Routing\\Middleware\\ThrottleRequests","Illuminate\\Routing\\Middleware\\ThrottleRequestsWithRedis","Illuminate\\Contracts\\Session\\Middleware\\AuthenticatesSessions","Illuminate\\Routing\\Middleware\\SubstituteBindings","Illuminate\\Auth\\Middleware\\Authorize"]}}},"listeners":[],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:07:42, ?, event, a0797a79-2f0b-4388-a808-f9c642e8b161), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {"name":"cache:clearing","payload":[null,[]],"listeners":[],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:07:42, ?, event, a0797a79-9eb9-4cd4-92b6-988d16cf0209), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {"name":"cache:cleared","payload":[null,[]],"listeners":[{"name":"Closure at D:\\hassan code\\shofy\\platform\\core\\base\\src\\Providers\\EventServiceProvider.php[147:155]","queued":false}],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:07:42, ?, event, a0797a79-a562-488b-ba86-ec898ae92502), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {"command":"optimize:clear","exit_code":0,"arguments":{"command":"optimize:clear"},"options":{"help":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:07:42, ?, command, a0797a79-ab6d-4c2c-9c47-d5d9f480a70e)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'shofy.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (a0797a79-ac65-4c0d-b827-329b4e8d8011, {\"connection\":\"mysql\",\"driver\":\"mysql\",\"bindings\":[],\"sql\":\"select * from `languages` order by `lang_order` asc\",\"time\":\"0.88\",\"slow\":false,\"file\":\"D:\\\\hassan code\\\\shofy\\\\platform\\\\core\\\\base\\\\src\\\\Models\\\\BaseQueryBuilder.php\",\"line\":48,\"hash\":\"b46aa2b5b43866595c382f2af177e0c6\",\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:07:42, ?, query, a0797a78-e9f4-4d9e-8d6d-302b426bb89f), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {\"action\":\"retrieved\",\"model\":\"Botble\\\\Language\\\\Models\\\\Language\",\"count\":1,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:07:42, ?, model, a0797a78-ecfb-41b5-a71f-9e7a4274e56e), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {\"name\":\"Kris\\\\LaravelFormBuilder\\\\Events\\\\FormComponentRegistering\",\"payload\":{\"form\":{\"class\":\"Kris\\\\LaravelFormBuilder\\\\Supports\\\\FormBuilder\",\"properties\":[]}},\"listeners\":[{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\core\\\\base\\\\src\\\\Providers\\\\FormServiceProvider.php[41:252]\",\"queued\":false}],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:07:42, ?, event, a0797a79-139f-4453-99f1-96a7f30b6648), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {\"name\":\"Kris\\\\LaravelFormBuilder\\\\Events\\\\FormComponentRegistered\",\"payload\":{\"form\":{\"class\":\"Kris\\\\LaravelFormBuilder\\\\Supports\\\\FormBuilder\",\"properties\":[]}},\"listeners\":[],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:07:42, ?, event, a0797a79-13fa-4c3e-b294-2ad034405fc8), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {\"name\":\"Botble\\\\Theme\\\\Events\\\\ThemeRoutingBeforeEvent\",\"payload\":{\"router\":{\"class\":\"Illuminate\\\\Routing\\\\Router\",\"properties\":{\"middlewarePriority\":[\"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\HandlePrecognitiveRequests\",\"Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies\",\"Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse\",\"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\"Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession\",\"Illuminate\\\\Contracts\\\\Auth\\\\Middleware\\\\AuthenticatesRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequestsWithRedis\",\"Illuminate\\\\Contracts\\\\Session\\\\Middleware\\\\AuthenticatesSessions\",\"Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings\",\"Illuminate\\\\Auth\\\\Middleware\\\\Authorize\"]}}},\"listeners\":[{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\blog\\\\src\\\\Providers\\\\BlogServiceProvider.php[76:82]\",\"queued\":false},{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\ecommerce\\\\routes\\\\base.php[209:249]\",\"queued\":false},{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\ecommerce\\\\src\\\\Providers\\\\EcommerceServiceProvider.php[427:434]\",\"queued\":false},{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\gallery\\\\src\\\\Providers\\\\GalleryServiceProvider.php[54:56]\",\"queued\":false}],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:07:42, ?, event, a0797a79-2e7e-473d-9f54-dafc78768cce), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {\"name\":\"Botble\\\\Theme\\\\Events\\\\ThemeRoutingAfterEvent\",\"payload\":{\"router\":{\"class\":\"Illuminate\\\\Routing\\\\Router\",\"properties\":{\"middlewarePriority\":[\"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\HandlePrecognitiveRequests\",\"Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies\",\"Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse\",\"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\"Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession\",\"Illuminate\\\\Contracts\\\\Auth\\\\Middleware\\\\AuthenticatesRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequestsWithRedis\",\"Illuminate\\\\Contracts\\\\Session\\\\Middleware\\\\AuthenticatesSessions\",\"Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings\",\"Illuminate\\\\Auth\\\\Middleware\\\\Authorize\"]}}},\"listeners\":[],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:07:42, ?, event, a0797a79-2f0b-4388-a808-f9c642e8b161), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {\"name\":\"cache:clearing\",\"payload\":[null,[]],\"listeners\":[],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:07:42, ?, event, a0797a79-9eb9-4cd4-92b6-988d16cf0209), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {\"name\":\"cache:cleared\",\"payload\":[null,[]],\"listeners\":[{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\core\\\\base\\\\src\\\\Providers\\\\EventServiceProvider.php[147:155]\",\"queued\":false}],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:07:42, ?, event, a0797a79-a562-488b-ba86-ec898ae92502), (a0797a79-ac65-4c0d-b827-329b4e8d8011, {\"command\":\"optimize:clear\",\"exit_code\":0,\"arguments\":{\"command\":\"optimize:clear\"},\"options\":{\"help\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:07:42, ?, command, a0797a79-ab6d-4c2c-9c47-d5d9f480a70e)) at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3498): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(664): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(289): call_user_func(Object(Closure))
#9 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(656): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1278): Illuminate\\Container\\Container->call(Object(Closure))
#17 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#18 D:\\hassan code\\shofy\\artisan(20): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'shofy.telescope_entries' doesn't exist at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:39)
[stacktrace]
#0 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(39): PDO->prepare('insert into `te...')
#1 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3498): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(664): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(289): call_user_func(Object(Closure))
#11 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(656): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1278): Illuminate\\Container\\Container->call(Object(Closure))
#19 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#20 D:\\hassan code\\shofy\\artisan(20): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 {main}
"} 
[2025-11-29 17:09:03] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'shofy.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (a0797af4-091f-47a2-a388-3aac813c1e49, {"connection":"mysql","driver":"mysql","bindings":[],"sql":"select * from `languages` order by `lang_order` asc","time":"0.80","slow":false,"file":"D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php","line":48,"hash":"b46aa2b5b43866595c382f2af177e0c6","hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:09:02, ?, query, a0797af3-6979-4c2c-8107-f83484a98f63), (a0797af4-091f-47a2-a388-3aac813c1e49, {"action":"retrieved","model":"Botble\\Language\\Models\\Language","count":1,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:09:02, ?, model, a0797af3-6c6c-49bc-a155-c92fa9420bf5), (a0797af4-091f-47a2-a388-3aac813c1e49, {"name":"Kris\\LaravelFormBuilder\\Events\\FormComponentRegistering","payload":{"form":{"class":"Kris\\LaravelFormBuilder\\Supports\\FormBuilder","properties":[]}},"listeners":[{"name":"Closure at D:\\hassan code\\shofy\\platform\\core\\base\\src\\Providers\\FormServiceProvider.php[41:252]","queued":false}],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:09:02, ?, event, a0797af3-90c8-4ff4-ab47-20b7155e27ab), (a0797af4-091f-47a2-a388-3aac813c1e49, {"name":"Kris\\LaravelFormBuilder\\Events\\FormComponentRegistered","payload":{"form":{"class":"Kris\\LaravelFormBuilder\\Supports\\FormBuilder","properties":[]}},"listeners":[],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:09:02, ?, event, a0797af3-9118-4357-bb9a-7efc81328784), (a0797af4-091f-47a2-a388-3aac813c1e49, {"name":"Botble\\Theme\\Events\\ThemeRoutingBeforeEvent","payload":{"router":{"class":"Illuminate\\Routing\\Router","properties":{"middlewarePriority":["Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful","Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests","Illuminate\\Cookie\\Middleware\\EncryptCookies","Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse","Illuminate\\Session\\Middleware\\StartSession","Illuminate\\View\\Middleware\\ShareErrorsFromSession","Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests","Illuminate\\Routing\\Middleware\\ThrottleRequests","Illuminate\\Routing\\Middleware\\ThrottleRequestsWithRedis","Illuminate\\Contracts\\Session\\Middleware\\AuthenticatesSessions","Illuminate\\Routing\\Middleware\\SubstituteBindings","Illuminate\\Auth\\Middleware\\Authorize"]}}},"listeners":[{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\blog\\src\\Providers\\BlogServiceProvider.php[76:82]","queued":false},{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\routes\\base.php[209:249]","queued":false},{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Providers\\EcommerceServiceProvider.php[427:434]","queued":false},{"name":"Closure at D:\\hassan code\\shofy\\platform\\plugins\\gallery\\src\\Providers\\GalleryServiceProvider.php[54:56]","queued":false}],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:09:02, ?, event, a0797af3-aa01-49af-80ae-e622f63008d8), (a0797af4-091f-47a2-a388-3aac813c1e49, {"name":"Botble\\Theme\\Events\\ThemeRoutingAfterEvent","payload":{"router":{"class":"Illuminate\\Routing\\Router","properties":{"middlewarePriority":["Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful","Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests","Illuminate\\Cookie\\Middleware\\EncryptCookies","Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse","Illuminate\\Session\\Middleware\\StartSession","Illuminate\\View\\Middleware\\ShareErrorsFromSession","Illuminate\\Contracts\\Auth\\Middleware\\AuthenticatesRequests","Illuminate\\Routing\\Middleware\\ThrottleRequests","Illuminate\\Routing\\Middleware\\ThrottleRequestsWithRedis","Illuminate\\Contracts\\Session\\Middleware\\AuthenticatesSessions","Illuminate\\Routing\\Middleware\\SubstituteBindings","Illuminate\\Auth\\Middleware\\Authorize"]}}},"listeners":[],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:09:02, ?, event, a0797af3-aa5c-4065-a286-f232505502f4), (a0797af4-091f-47a2-a388-3aac813c1e49, {"name":"cache:clearing","payload":[null,[]],"listeners":[],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:09:02, ?, event, a0797af3-fbf2-4925-b4b7-5f64f373a049), (a0797af4-091f-47a2-a388-3aac813c1e49, {"name":"cache:cleared","payload":[null,[]],"listeners":[{"name":"Closure at D:\\hassan code\\shofy\\platform\\core\\base\\src\\Providers\\EventServiceProvider.php[147:155]","queued":false}],"broadcast":false,"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:09:02, ?, event, a0797af4-01af-4c4f-a2a1-8186006558fc), (a0797af4-091f-47a2-a388-3aac813c1e49, {"command":"optimize:clear","exit_code":0,"arguments":{"command":"optimize:clear"},"options":{"help":false,"quiet":false,"verbose":false,"version":false,"ansi":null,"no-interaction":false,"env":null},"hostname":"DESKTOP-3VTK8Q0"}, 2025-11-29 17:09:03, ?, command, a0797af4-07d9-40c2-ab19-566ad25a19a5)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'shofy.telescope_entries' doesn't exist (Connection: mysql, SQL: insert into `telescope_entries` (`batch_id`, `content`, `created_at`, `family_hash`, `type`, `uuid`) values (a0797af4-091f-47a2-a388-3aac813c1e49, {\"connection\":\"mysql\",\"driver\":\"mysql\",\"bindings\":[],\"sql\":\"select * from `languages` order by `lang_order` asc\",\"time\":\"0.80\",\"slow\":false,\"file\":\"D:\\\\hassan code\\\\shofy\\\\platform\\\\core\\\\base\\\\src\\\\Models\\\\BaseQueryBuilder.php\",\"line\":48,\"hash\":\"b46aa2b5b43866595c382f2af177e0c6\",\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:09:02, ?, query, a0797af3-6979-4c2c-8107-f83484a98f63), (a0797af4-091f-47a2-a388-3aac813c1e49, {\"action\":\"retrieved\",\"model\":\"Botble\\\\Language\\\\Models\\\\Language\",\"count\":1,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:09:02, ?, model, a0797af3-6c6c-49bc-a155-c92fa9420bf5), (a0797af4-091f-47a2-a388-3aac813c1e49, {\"name\":\"Kris\\\\LaravelFormBuilder\\\\Events\\\\FormComponentRegistering\",\"payload\":{\"form\":{\"class\":\"Kris\\\\LaravelFormBuilder\\\\Supports\\\\FormBuilder\",\"properties\":[]}},\"listeners\":[{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\core\\\\base\\\\src\\\\Providers\\\\FormServiceProvider.php[41:252]\",\"queued\":false}],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:09:02, ?, event, a0797af3-90c8-4ff4-ab47-20b7155e27ab), (a0797af4-091f-47a2-a388-3aac813c1e49, {\"name\":\"Kris\\\\LaravelFormBuilder\\\\Events\\\\FormComponentRegistered\",\"payload\":{\"form\":{\"class\":\"Kris\\\\LaravelFormBuilder\\\\Supports\\\\FormBuilder\",\"properties\":[]}},\"listeners\":[],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:09:02, ?, event, a0797af3-9118-4357-bb9a-7efc81328784), (a0797af4-091f-47a2-a388-3aac813c1e49, {\"name\":\"Botble\\\\Theme\\\\Events\\\\ThemeRoutingBeforeEvent\",\"payload\":{\"router\":{\"class\":\"Illuminate\\\\Routing\\\\Router\",\"properties\":{\"middlewarePriority\":[\"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\HandlePrecognitiveRequests\",\"Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies\",\"Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse\",\"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\"Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession\",\"Illuminate\\\\Contracts\\\\Auth\\\\Middleware\\\\AuthenticatesRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequestsWithRedis\",\"Illuminate\\\\Contracts\\\\Session\\\\Middleware\\\\AuthenticatesSessions\",\"Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings\",\"Illuminate\\\\Auth\\\\Middleware\\\\Authorize\"]}}},\"listeners\":[{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\blog\\\\src\\\\Providers\\\\BlogServiceProvider.php[76:82]\",\"queued\":false},{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\ecommerce\\\\routes\\\\base.php[209:249]\",\"queued\":false},{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\ecommerce\\\\src\\\\Providers\\\\EcommerceServiceProvider.php[427:434]\",\"queued\":false},{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\plugins\\\\gallery\\\\src\\\\Providers\\\\GalleryServiceProvider.php[54:56]\",\"queued\":false}],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:09:02, ?, event, a0797af3-aa01-49af-80ae-e622f63008d8), (a0797af4-091f-47a2-a388-3aac813c1e49, {\"name\":\"Botble\\\\Theme\\\\Events\\\\ThemeRoutingAfterEvent\",\"payload\":{\"router\":{\"class\":\"Illuminate\\\\Routing\\\\Router\",\"properties\":{\"middlewarePriority\":[\"Laravel\\\\Sanctum\\\\Http\\\\Middleware\\\\EnsureFrontendRequestsAreStateful\",\"Illuminate\\\\Foundation\\\\Http\\\\Middleware\\\\HandlePrecognitiveRequests\",\"Illuminate\\\\Cookie\\\\Middleware\\\\EncryptCookies\",\"Illuminate\\\\Cookie\\\\Middleware\\\\AddQueuedCookiesToResponse\",\"Illuminate\\\\Session\\\\Middleware\\\\StartSession\",\"Illuminate\\\\View\\\\Middleware\\\\ShareErrorsFromSession\",\"Illuminate\\\\Contracts\\\\Auth\\\\Middleware\\\\AuthenticatesRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequests\",\"Illuminate\\\\Routing\\\\Middleware\\\\ThrottleRequestsWithRedis\",\"Illuminate\\\\Contracts\\\\Session\\\\Middleware\\\\AuthenticatesSessions\",\"Illuminate\\\\Routing\\\\Middleware\\\\SubstituteBindings\",\"Illuminate\\\\Auth\\\\Middleware\\\\Authorize\"]}}},\"listeners\":[],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:09:02, ?, event, a0797af3-aa5c-4065-a286-f232505502f4), (a0797af4-091f-47a2-a388-3aac813c1e49, {\"name\":\"cache:clearing\",\"payload\":[null,[]],\"listeners\":[],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:09:02, ?, event, a0797af3-fbf2-4925-b4b7-5f64f373a049), (a0797af4-091f-47a2-a388-3aac813c1e49, {\"name\":\"cache:cleared\",\"payload\":[null,[]],\"listeners\":[{\"name\":\"Closure at D:\\\\hassan code\\\\shofy\\\\platform\\\\core\\\\base\\\\src\\\\Providers\\\\EventServiceProvider.php[147:155]\",\"queued\":false}],\"broadcast\":false,\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:09:02, ?, event, a0797af4-01af-4c4f-a2a1-8186006558fc), (a0797af4-091f-47a2-a388-3aac813c1e49, {\"command\":\"optimize:clear\",\"exit_code\":0,\"arguments\":{\"command\":\"optimize:clear\"},\"options\":{\"help\":false,\"quiet\":false,\"verbose\":false,\"version\":false,\"ansi\":null,\"no-interaction\":false,\"env\":null},\"hostname\":\"DESKTOP-3VTK8Q0\"}, 2025-11-29 17:09:03, ?, command, a0797af4-07d9-40c2-ab19-566ad25a19a5)) at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#1 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#2 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3498): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#3 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#4 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#5 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#6 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(664): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#7 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#8 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(289): call_user_func(Object(Closure))
#9 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(656): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#10 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#11 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#12 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#15 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#16 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1278): Illuminate\\Container\\Container->call(Object(Closure))
#17 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#18 D:\\hassan code\\shofy\\artisan(20): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#19 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'shofy.telescope_entries' doesn't exist at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:39)
[stacktrace]
#0 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(39): PDO->prepare('insert into `te...')
#1 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `te...', Array)
#2 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `te...', Array, Object(Closure))
#3 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `te...', Array, Object(Closure))
#4 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3498): Illuminate\\Database\\MySqlConnection->insert('insert into `te...', Array)
#5 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(145): Illuminate\\Database\\Query\\Builder->insert(Array)
#6 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php(240): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->Laravel\\Telescope\\Storage\\{closure}(Object(Illuminate\\Support\\Collection), 0)
#7 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Storage\\DatabaseEntriesRepository.php(144): Illuminate\\Support\\Collection->each(Object(Closure))
#8 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(664): Laravel\\Telescope\\Storage\\DatabaseEntriesRepository->store(Object(Illuminate\\Support\\Collection))
#9 [internal function]: Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#10 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(289): call_user_func(Object(Closure))
#11 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\Telescope.php(656): Laravel\\Telescope\\Telescope::withoutRecording(Object(Closure))
#12 D:\\hassan code\\shofy\\vendor\\laravel\\telescope\\src\\ListensForStorageOpportunities.php(65): Laravel\\Telescope\\Telescope::store(Object(Laravel\\Telescope\\Storage\\DatabaseEntriesRepository))
#13 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Telescope\\Telescope::Laravel\\Telescope\\{closure}()
#14 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#17 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#18 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1278): Illuminate\\Container\\Container->call(Object(Closure))
#19 D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(220): Illuminate\\Foundation\\Application->terminate()
#20 D:\\hassan code\\shofy\\artisan(20): Illuminate\\Foundation\\Console\\Kernel->terminate(Object(Symfony\\Component\\Console\\Input\\ArgvInput), 0)
#21 {main}
"} 
[2025-11-29 17:14:06] local.ERROR: Maximum execution time of 300 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 300 seconds exceeded at D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:95)
[stacktrace]
#0 {main}
"} 
