<?php

use Bo<PERSON><PERSON>\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Payment\Enums\PaymentMethodEnum;
use Bo<PERSON>ble\Payment\Enums\PaymentStatusEnum;
use Botble\Media\Facades\RvMedia;
    $payment_description = null;
$logo = get_ecommerce_setting('company_logo_for_invoicing') ?: (theme_option(
            'logo_in_invoices'
        ) ?: theme_option('logo'));


$logo_full_path= RvMedia::getRealPath($logo);        
        if (
            is_plugin_active('payment') &&
            $invoice->payment->payment_channel == PaymentMethodEnum::BANK_TRANSFER &&
            $invoice->payment->status == PaymentStatusEnum::PENDING
        ) {
            $payment_description = BaseHelper::clean(
                get_payment_setting('description', $invoice->payment->payment_channel)
            );
        }
        $company_name = get_ecommerce_setting('company_name_for_invoicing') ?: get_ecommerce_setting('store_name');  
        
           $company_address = get_ecommerce_setting('company_address_for_invoicing');

             $company_phone = get_ecommerce_setting('company_phone_for_invoicing') ?: get_ecommerce_setting('store_phone');
        $company_email = get_ecommerce_setting('company_email_for_invoicing') ?: get_ecommerce_setting('store_email');
        $company_tax_id = get_ecommerce_setting('company_tax_id_for_invoicing') ?: get_ecommerce_setting(
            'store_vat_number'
        );
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ trans('plugins/ecommerce::order.invoice_for_order') }} {{ $invoice->code }}</title>
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazirmatn@v33.003/fonts.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Vazirmatn, 'Segoe UI', Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        .invoice-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding: 30px;
            margin: 30px auto;
            max-width: 1000px;
        }
        .invoice-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            max-height: 80px;
            max-width: 200px;
        }
        .invoice-title {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
        }
        .company-info, .customer-info {
            margin-bottom: 20px;
        }
        .info-label {
            font-weight: 600;
            color: #7f8c8d;
            margin-bottom: 5px;
        }
        .info-value {
            margin-bottom: 10px;
        }
        .table thead th {
            background-color: #f8f9fa;
            border-bottom-width: 1px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
            letter-spacing: 0.5px;
            text-align: right;
        }
        .total-row {
            font-weight: 600;
            background-color: #f8f9fa;
        }
        .grand-total {
            font-size: 1.2rem;
            color: #e74c3c;
            font-weight: 700;
        }
        .payment-card {
            border-right: 4px solid #3498db;
            border-radius: 5px;
        }
        .stamp {
            position: absolute;
            transform: rotate(-15deg);
            opacity: 0.7;
            font-size: 2rem;
            font-weight: 700;
            padding: 10px 30px;
            border: 3px solid;
            border-radius: 5px;
        }
        .stamp-completed {
            color: #27ae60;
            border-color: #27ae60;
        }
        .stamp-failed {
            color: #e74c3c;
            border-color: #e74c3c;
        }
        .stamp-pending {
            color: #f39c12;
            border-color: #f39c12;
        }
        .note-box {
            background-color: #f8f9fa;
            border-right: 4px solid #95a5a6;
            padding: 15px;
            border-radius: 5px;
        }
        .text-end {
            text-align: left !important;
        }
        .text-start {
            text-align: right !important;
        }
        @media print {
            body {
                background-color: white;
            }
            .invoice-container {
                box-shadow: none;
                padding: 0;
                margin: 0;
            }
            .no-print {
                display: none !important;
            }
            .stamp {
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="invoice-container position-relative">
            <!-- Stamp for status -->
            @if (get_ecommerce_setting('enable_invoice_stamp', 1) == 1)
                @if ($invoice->status == 'canceled')
                    <div class="stamp stamp-failed position-absolute top-50 start-50 translate-middle">
                        {{ $invoice->status == 'canceled' ? 'ملغية' : $invoice->status }}
                    </div>
                @elseif ($invoice->payment->status->label())
                    <div class="stamp @if ($invoice->payment->status->getValue() == 'completed') stamp-completed @else stamp-failed @endif position-absolute top-50 start-50 translate-middle">
                        {{ $invoice->payment->status->getValue() == 'completed' ? 'مكتمل' : ($invoice->payment->status->getValue() == 'pending' ? 'قيد الانتظار' : 'فاشل') }}
                    </div>
                @endif
            @endif

            <!-- Header -->
            <div class="invoice-header row">
                <div class="col-md-6">
                    @if ($logo)
                        <img src="{{ $logo_full_path }}" class="logo img-fluid mb-3" alt="{{ $company_name }}">
                    @endif
                </div>
                <div class="col-md-6 text-start">
                    <h1 class="invoice-title mb-3">فاتورة</h1>
                    <div class="mb-2">
                        <span class="fw-bold">رقم الفاتورة: </span>
                        {{ $invoice->code }}
                    </div>
                    <div class="mb-2">
                        <span class="fw-bold">رقم الطلب: </span>
                        {{ $invoice->reference->code }}
                    </div>
                    @if ($invoice->created_at)
                        <div class="mb-2">
                            <span class="fw-bold">التاريخ: </span>
                            {{ $invoice->created_at->format(env('CMS_DATE_FORMAT')) }}
                        </div>
                    @endif
                </div>
            </div>

            <!-- Company and Customer Info -->
            <div class="row mb-4">
                <div class="col-md-6  col-sm-6  company-info">
                    <h5 class="fw-bold mb-3">من</h5>
                    @if ($company_name)
                        <div class="info-value fw-bold">{{ $company_name }}</div>
                    @endif
                    @if ($company_address)
                        <div class="info-value">{{ $company_address }}</div>
                    @endif
                    @if ($company_phone)
                        <div class="info-value"><i class="fas fa-phone me-2"></i>{{ $company_phone }}</div>
                    @endif
                    @if ($company_email)
                        <div class="info-value"><i class="fas fa-envelope me-2"></i>{{ $company_email }}</div>
                    @endif
                    @if ($company_tax_id)
                        <div class="info-value"><i class="fas fa-id-card me-2"></i>الرقم الضريبي: {{ $company_tax_id }}</div>
                    @endif
                </div>
                <div class="col-md-6 col-sm-6 customer-info">
                    <h5 class="fw-bold mb-3">إلى</h5>
                    @if ($invoice->customer_name)
                        <div class="info-value fw-bold">{{ $invoice->customer_name }}</div>
                    @endif
                    @if ($invoice->customer_email)
                        <div class="info-value"><i class="fas fa-envelope me-2"></i>{{ $invoice->customer_email }}</div>
                    @endif
                    @if ($invoice->customer_phone)
                        <div class="info-value"><i class="fas fa-phone me-2"></i>{{ $invoice->customer_phone }}</div>
                    @endif
                    @if ($invoice->customer_address)
                        <div class="info-value"><i class="fas fa-map-marker-alt me-2"></i>{{ $invoice->customer_address }}</div>
                    @endif
                    @if ($invoice->customer_tax_id)
                        <div class="info-value"><i class="fas fa-id-card me-2"></i>الرقم الضريبي: {{ $invoice->customer_tax_id }}</div>
                    @endif
                </div>
            </div>

            <!-- Invoice Note -->
            @if ($invoice->description)
                <div class="note-box mb-4">
                    <h6 class="fw-bold mb-2">ملاحظة</h6>
                    <p class="mb-0">{{ $invoice->description }}</p>
                </div>
            @endif

            <!-- Items Table -->
            <div class="table-responsive mb-5">
                <table class="table">
                    <thead>
                        <tr>
                            <th class="pe-0">المنتج</th>
                            <th>الخيارات</th>
                            <th class="text-center">الكمية</th>
                            <th class="text-end">السعر</th>
                            <th class="text-start ps-0">المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($invoice->items as $item)
                            <tr>
                                <td class="pe-0">
                                    {{ $item->name }}
                                    @if ($item->options['sku'])
                                        <div class="text-muted small">SKU: {{ $item->options['sku'] }}</div>
                                    @endif
                                </td>
                                <td>
                                    @if ($item->options)
                                        @if ($item->options['attributes'])
                                            <div class="small"><span class="text-muted">الخصائص:</span> {{ $item->options['attributes'] }}</div>
                                        @endif
                                        @if ($item->product_options)
                                            <div class="small"><span class="text-muted">خيارات المنتج:</span> {{ $item->options['product_options'] }}</div>
                                        @endif
                                        @if ($item->license_code)
                                            <div class="small"><span class="text-muted">رمز الترخيص:</span> {{ $item->options['license_code'] }}</div>
                                        @endif
                                    @endif
                                </td>
                                <td class="text-center">{{ $item->qty }}</td>
                                <td class="text-end">{{ format_price($item->price) }}</td>
                                <td class="text-start ps-0 fw-bold">{{ format_price($item->sub_total) }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Totals -->
            <div class="row justify-content-start">
                <div class="col-md-5">
                    <table class="table">
                        <tbody>
                            <tr>
                                <td class="text-start">الكمية الإجمالية</td>
                                <td class="text-start fw-bold" style="width: 120px;">{{ $invoice->items->sum('qty') }}</td>
                            </tr>
                            <tr>
                                <td class="text-start">المجموع الجزئي</td>
                                <td class="text-start fw-bold">{{ format_price($invoice->sub_total) }}</td>
                            </tr>
                            @if ($invoice->tax_amount > 0)
                                <tr>
                                    <td class="text-start">
                                        الضريبة <small>({{ $invoice->taxClassesName }})</small>
                                    </td>
                                    <td class="text-start fw-bold">{{ format_price($invoice->tax_amount) }}</td>
                                </tr>
                            @endif
                            @if ($invoice->shipping_amount > 0)
                                <tr>
                                    <td class="text-start">رسوم الشحن</td>
                                    <td class="text-start fw-bold">{{ format_price($invoice->shipping_amount) }}</td>
                                </tr>
                            @endif
                            @if ($invoice->discount_amount > 0)
                                <tr>
                                    <td class="text-start">الخصم</td>
                                    <td class="text-start fw-bold">-{{ format_price($invoice->discount_amount) }}</td>
                                </tr>
                            @endif
                            <tr class="grand-total">
                                <td class="text-start">المبلغ الإجمالي</td>
                                <td class="text-start">{{ format_price($invoice->amount) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

     
        </div>

        <!-- Print Button -->
        <div class="text-center mb-4 no-print">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print me-2"></i>طباعة الفاتورة
            </button>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>