<?php

namespace Bo<PERSON>ble\Ecommerce\Http\Controllers\Fronts;

use Botble\Ecommerce\Facades\EcommerceHelper;
use Botble\Ecommerce\Facades\ProductCategoryHelper;
use Bo<PERSON>ble\Ecommerce\Http\Controllers\BaseController;
use Botble\Ecommerce\Models\ProductCategory;
use Botble\Ecommerce\Services\Products\GetProductService;
use Botble\Support\Services\Cache\Cache;
use Botble\Theme\Facades\Theme;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class PublicAjaxController extends BaseController
{
    public function ajaxSearchProducts(Request $request, GetProductService $productService)
    {
        $request->merge(['num' => 12]);

        $with = EcommerceHelper::withProductEagerLoadingRelations();

        $products = $productService->getProduct($request, null, null, $with);

        $queries = $request->input();

        foreach ($queries as $key => $query) {
            if (! $query || $key == 'num' || (is_array($query) && ! Arr::get($query, 0))) {
                unset($queries[$key]);
            }
        }

        $total = $products->count();

        return $this
            ->httpResponse()
            ->setData(view(EcommerceHelper::viewPath('includes.ajax-search-results'), compact('products', 'queries'))->render())
            ->setMessage($total != 1 ? __(':total Products found', compact('total')) : __(':total Product found', compact('total')));
    }

    public function ajaxGetCategoriesDropdown()
    {
        // Create cache key for categories dropdown
        $cache = Cache::make(ProductCategory::class);
        $categoriesDropdownView = Theme::getThemeNamespace('partials.product-categories-dropdown');

        $cacheKey = 'ajax_categories_dropdown_' . md5(serialize([
            'view_exists' => view()->exists($categoriesDropdownView),
            'theme' => Theme::getThemeName(),
            'locale' => app()->getLocale(),
        ]));

        // Cache the entire response for 2 hours
        return $cache->remember($cacheKey, Carbon::now()->addHours(2), function () use ($categoriesDropdownView) {
            $selectHtml = ProductCategoryHelper::renderProductCategoriesSelect();
            $dropdownHtml = null;

            if (view()->exists($categoriesDropdownView)) {
                $dropdownHtml = view($categoriesDropdownView)->render();
            }

            return $this
                ->httpResponse()
                ->setData([
                    'select' => $selectHtml,
                    'dropdown' => $dropdownHtml,
                ]);
        });
    }
}
