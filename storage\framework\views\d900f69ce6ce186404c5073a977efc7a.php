<?php
    $itemsPerRow = $shortcode->items_per_row ?: 4;
    $itemsPerRow = $shortcode->with_sidebar ? $itemsPerRow - 1 : $itemsPerRow;
?>

<section class="tp-product-arrival-area pt-30 pb-30">
    <div class="container">
        <?php echo Theme::partial('section-title', compact('shortcode')); ?>


        <?php if($shortcode->with_sidebar): ?>
            <div class="row">
                <div class="col-xl-4 col-lg-5">
                    <?php echo $__env->make(Theme::getThemeNamespace('partials.shortcodes.ecommerce-products.partials.sidebar'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div class="col-xl-8 col-lg-7">
                    <?php endif; ?>

                    <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product-items'), ['itemsPerRow' => $itemsPerRow], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <?php if($shortcode->with_sidebar): ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>
<?php /**PATH D:\hassan code\shofy\platform\themes/shofy/partials/shortcodes/ecommerce-products/grid.blade.php ENDPATH**/ ?>