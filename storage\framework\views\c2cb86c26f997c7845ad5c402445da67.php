<div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['tp-product-item transition-3 mb-25', 'tp-product-item-3 tp-product-style-primary mb-50' => $style === 3, $class ?? null]); ?>">
    <div class="tp-product-thumb p-relative fix m-img">
        <a href="<?php echo e($product->url); ?>">
            <?php echo e(RvMedia::image($product->image, $product->name, 'medium')); ?>

        </a>

        <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product.badges'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product.style-1.actions'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>

    <div class="tp-product-content">
        <?php echo apply_filters('ecommerce_before_product_item_content_renderer', null, $product); ?>


        <?php if(is_plugin_active('marketplace') && $product->store): ?>
            <div class="tp-product-category">
                <a href="<?php echo e($product->store->url); ?>"><?php echo e($product->store->name); ?></a>
            </div>
        <?php endif; ?>
        <h3 class="text-truncate tp-product-title">
            <a href="<?php echo e($product->url); ?>" title="<?php echo e($product->name); ?>">
                <?php echo BaseHelper::clean($product->name); ?>

            </a>
        </h3>

        <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['tp-product-price-review' => theme_option('product_listing_review_style', 'default') !== 'default']); ?>">
            <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product.style-1.rating'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

            <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product.style-1.price'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>

        <?php if($withCountdown ?? false): ?>
            <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product.countdown'), compact('endDate'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>

        <?php echo apply_filters('ecommerce_after_product_item_content_renderer', null, $product); ?>

    </div>
</div>
<?php /**PATH D:\hassan code\shofy\platform\themes/shofy/views/ecommerce/includes/product/style-1/grid.blade.php ENDPATH**/ ?>