<?php

/**
 * Simple test script to verify the AJAX products endpoint
 * Run this from the command line: php test_ajax_products.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Test the endpoint
function testAjaxProducts($url) {
    echo "Testing: $url\n";
    
    // Create a request
    $request = Request::create($url, 'GET');
    $request->headers->set('Accept', 'application/json');
    $request->headers->set('X-Requested-With', 'XMLHttpRequest');
    
    try {
        global $kernel;
        $response = $kernel->handle($request);
        
        echo "Status: " . $response->getStatusCode() . "\n";
        
        if ($response->getStatusCode() === 200) {
            $data = json_decode($response->getContent(), true);
            
            if (isset($data['data'])) {
                echo "Count: " . ($data['data']['count'] ?? 'N/A') . "\n";
                echo "HTML Length: " . strlen($data['data']['html'] ?? '') . " characters\n";
                echo "Error: " . ($data['error'] ? 'true' : 'false') . "\n";
            } else {
                echo "Response: " . $response->getContent() . "\n";
            }
        } else {
            echo "Error Response: " . $response->getContent() . "\n";
        }
        
        echo "---\n";
        
    } catch (Exception $e) {
        echo "Exception: " . $e->getMessage() . "\n";
        echo "---\n";
    }
}

// Test different endpoints
echo "=== AJAX Products Endpoint Tests ===\n\n";

testAjaxProducts('/ajax/products?limit=8&type=all');
testAjaxProducts('/ajax/products?limit=5&type=featured');
testAjaxProducts('/ajax/products?limit=3&type=trending');
testAjaxProducts('/ajax/products?limit=4&type=on-sale');
testAjaxProducts('/ajax/products?limit=6&type=top-rated');

echo "\n=== Test with search query ===\n\n";
testAjaxProducts('/ajax/products?q=test&limit=5');

echo "\n=== Cache test (same request twice) ===\n\n";
$start = microtime(true);
testAjaxProducts('/ajax/products?limit=8&type=all');
$time1 = microtime(true) - $start;
echo "First request time: " . round($time1 * 1000, 2) . "ms\n\n";

$start = microtime(true);
testAjaxProducts('/ajax/products?limit=8&type=all');
$time2 = microtime(true) - $start;
echo "Second request time: " . round($time2 * 1000, 2) . "ms\n";

if ($time2 < $time1) {
    echo "✓ Second request was faster (likely cached)\n";
} else {
    echo "⚠ Second request was not faster\n";
}

echo "\nTests completed!\n";
