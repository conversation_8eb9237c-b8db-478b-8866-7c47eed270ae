<?php

namespace App\Console\Commands;

use App\Models\ScrapShopify;
use Bo<PERSON>ble\Blog\Models\Tag;
use <PERSON><PERSON>ble\Ecommerce\Enums\ProductTypeEnum;
use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\ProductAttribute;
use Bo<PERSON>ble\Ecommerce\Models\ProductAttributeSet;
use Botble\Ecommerce\Models\ProductCategory;
use Botble\Ecommerce\Models\ProductVariation;
use Bo<PERSON>ble\Ecommerce\Models\ProductVariationItem;
use Botble\Media\Facades\RvMedia;
use Botble\Media\Models\MediaFile;
use Botble\Media\Models\MediaFolder;
use Bo<PERSON>ble\Slug\Models\Slug;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class MegahardwareScraperCommand extends Command
{
    protected $signature = 'import:mega-hardware {--page=1 : Starting page} {--max-pages=100 : Maximum pages to import}';
    protected $description = 'Import products from Shopify store with Arabic and English support';

    protected string $baseUrl = 'https://mega-hardware.com';
    protected int $perPage = 250;
    protected int $currentPage;
    protected int $maxPages;

    public function handle(): int
    {
        $this->currentPage = (int) $this->option('page');
        $this->maxPages = (int) $this->option('max-pages');
        $importedCount = 0;
        $round=0;    


        $scrapShopifyModel = ScrapShopify::where('site',$this->baseUrl)->first();
   
        if(is_null($scrapShopifyModel)){
           $scrapShopifyModel = ScrapShopify::create([ 'site'=>$this->baseUrl,'pageNumber'=> $this->currentPage]);
        }else{
         $currentPage = (int) ScrapShopify::where('site',$this->baseUrl)->orderBy('pageNumber','DESC')->first()->pageNumber;
          ScrapShopify::where('site',$this->baseUrl)->delete(); 
          $currentPage ++;
          $scrapShopifyModel = ScrapShopify::create([ 'site'=>$this->baseUrl,'pageNumber'=> $this->currentPage]);
        }



        $this->info("Starting import from: {$this->baseUrl}");
        $this->info("Page range: {$this->currentPage} to {$this->maxPages}");

        do {
            try {
                $response = $this->fetchProductsPage($this->currentPage);
                
                if ($response->failed()) {
                    $this->error("Failed to fetch page {$this->currentPage}: HTTP {$response->status()}");
                    break;
                }

                $data = $response->json();
                $round ++;
                if (empty($data['products'])) {
                    $this->info("No more products found on page {$this->currentPage}");
                    break;
                }

                $this->info("Processing page {$this->currentPage} with " . count($data['products']) . " products");

                DB::transaction(function () use ($data, &$importedCount) {
                    foreach ($data['products'] as $productData) {
                        try {
                            $this->processProduct($productData);
                            $importedCount++;
                        } catch (Exception $e) {
                            $this->error("Failed to process product {$productData['id']}: {$e->getMessage()}");
                        }
                    }
                });

                $this->info("Successfully processed page {$this->currentPage}");
                $hasMoreProducts = count($data['products']) === $this->perPage;
                $this->currentPage++;

            } catch (Exception $e) {
                $this->error("Error processing page {$this->currentPage}: {$e->getMessage()}");
                break;
            }

        }
        while (
            count($data['products']) === $this->perPage && 
            (!$this->maxPages || $this->currentPage <= $this->maxPages)   &&  $round >= 5 
        );


         $scrapShopifyModel = ScrapShopify::create([ 'site'=>$this->baseUrl,'pageNumber'=> $this->currentPage]);

        $this->info("Import completed. Total products imported: {$importedCount}");
        return 0;
    }

    protected function fetchProductsPage(int $page)
    {
        $apiUrl = "{$this->baseUrl}/products.json?limit={$this->perPage}&page={$page}";
        $this->info("Fetching products from: {$apiUrl}");
        return Http::withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            'Cookie' => 'shopify_test_cookie=1;',
        ])
        ->timeout(30)
        ->retry(3, 1000)
        ->get($apiUrl);
    }

    protected function fetchProductByHandle(string $handle, string $locale = 'ar')
    {

        if($locale == 'ar'){
              $apiUrl = "{$this->baseUrl}/{$locale}/products/{$handle}.json";
        }else{
            $apiUrl = "{$this->baseUrl}/products/{$handle}.json";
        }
        $this->info("Fetching {$locale} version for {$handle} from: {$apiUrl}");
        
        try {
            $response = Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                'Cookie' => 'shopify_test_cookie=1;',
            ])
            ->timeout(30)
            ->retry(3, 1000)
            ->get($apiUrl);

            if ($response->successful()) {
                return $response->json('product');
            }
        } catch (Exception $e) {
            $this->warn("Failed to fetch {$locale} version for {$handle}: {$e->getMessage()}");
        }

        return null;
    }

    protected function processProduct(array $productData): void
    {
        $isArabic = $this->isArabicText($productData['title']);
        $arabicData = null;
        $englishData = null;

        if ($isArabic) {
            $arabicData = $productData;
            $englishData = $this->fetchProductByHandle($productData['handle'], 'en');
        } else {
            $englishData = $productData;
            $arabicData = $this->fetchProductByHandle($productData['handle'], 'ar');
        }

        $mainProduct = $this->createOrUpdateProduct($arabicData, $englishData);

        if ($mainProduct) {
            $this->processProductAttributes($arabicData ?? $englishData, $mainProduct);
            $this->processProductVariants($arabicData ?? $englishData, $mainProduct);
            $this->processProductImages($arabicData ?? $englishData, $mainProduct);
            $this->assignImagesToVariations($arabicData ?? $englishData);
            $this->processTags($arabicData ?? $englishData, $mainProduct);
            $this->processProductType($arabicData ?? $englishData, $mainProduct);
        
        }
    }

    protected function createOrUpdateProduct(?array $arabicData, ?array $englishData): ?Product
    {
        $primaryData = $arabicData ?? $englishData;
        
        if (!$primaryData) {
            $this->warn("No product data available");
            return null;
        }

        $firstVariant = $primaryData['variants'][0] ?? [];
        
        $product = Product::updateOrCreate(
            ['shopify_id' => $primaryData['id']],
            [
                'name' => $arabicData['title'] ?? $englishData['title'] ?? 'Untitled',
                'description' => $arabicData['body_html'] ?? '',
                'content' => $arabicData['body_html'] ?? '',
                'status' => $primaryData['status'] ?? 'published',
                'is_featured' => false,
                'price' => $this->parsePrice($firstVariant['price'] ?? 0),
                'sku' => $firstVariant['sku'] ?? null,
                'barcode' => $firstVariant['barcode'] ?? null,
                'quantity' => $firstVariant['inventory_quantity'] ?? 0,
                'weight' => $firstVariant['weight'] ?? null,
                'product_type' => ProductTypeEnum::PHYSICAL,
                'is_variation' => false,
                'with_storehouse_management' => false,
                'stock_status' => 'in_stock',
            ]
        );

        // Save English translation if available
        if ($englishData && $englishData['title']) {
            DB::table('ec_products_translations')->updateOrInsert(
                [
                    'lang_code' => 'en_US',
                    'ec_products_id' => $product->id,
                ],
                [
                    'name' => $englishData['title'],
                    'description' => $englishData['body_html'] ?? '',
                    'content' => $englishData['body_html'] ?? '',
                ]
            );
        }

        // Create slug from English title if available, otherwise from Arabic
        $slugTitle = $englishData['title'] ?? $arabicData['title'] ?? $product->name;
        $this->createSlug($product, $slugTitle);

        return $product;
    }

    protected function createSlug(Product $product, string $title): void
    {
        Slug::updateOrCreate(
            [
                'reference_id' => $product->id,
                'reference_type' => Product::class,
            ],
            [
                'key' => Str::slug($title),
                'prefix' => 'products',
            ]
        );
    }

    protected function processProductAttributes(array $productData, Product $mainProduct): void
    {
        if (empty($productData['options'])) {
            return;
        }

        // Fetch alternate language version for translations
        $isArabic = $this->isArabicText($productData['title']);
        $alternateData = $this->fetchProductByHandle(
            $productData['handle'], 
            $isArabic ? 'en' : 'ar'
        );

        foreach ($productData['options'] as $index => $option) {
            $attributeSet = ProductAttributeSet::updateOrCreate(
                ['title' => $option['name']],
                [
                    'slug' => Str::slug($option['name']),
                    'status' => 'published',
                    'order' => 0,
                    'display_layout' => 'text',
                    'is_searchable' => true,
                    'is_comparable' => true,
                    'is_use_in_product_listing' => true,
                    'use_image_from_product_variation' => true,
                ]
            );

            // Save attribute set translation
            if ($alternateData && isset($alternateData['options'][$index])) {
                $this->saveAttributeSetTranslation(
                    $attributeSet,
                    $option['name'],
                    $alternateData['options'][$index]['name'],
                    $isArabic
                );
            }

            $mainProduct->productAttributeSets()->syncWithoutDetaching([$attributeSet->id]);

            // Process attribute values
            foreach ($option['values'] as $valueIndex => $value) {
                $attribute = ProductAttribute::updateOrCreate(
                    [
                        'title' => $value,
                        'attribute_set_id' => $attributeSet->id,
                    ],
                    [
                        'slug' => Str::slug($value),
                        'order' => 0,
                    ]
                );

                // Save attribute value translation
                if ($alternateData && 
                    isset($alternateData['options'][$index]['values'][$valueIndex])) {
                    $this->saveAttributeTranslation(
                        $attribute,
                        $value,
                        $alternateData['options'][$index]['values'][$valueIndex],
                        $isArabic
                    );
                }
            }
        }
    }

    protected function saveAttributeSetTranslation(
        ProductAttributeSet $attributeSet,
        string $arabicTitle,
        string $englishTitle,
        bool $isArabic
    ): void {
        // Save English translation
        DB::table('ec_product_attribute_sets_translations')->updateOrInsert(
            [
                'lang_code' => 'en_US',
                'ec_product_attribute_sets_id' => $attributeSet->id,
            ],
            [
                'title' => $isArabic ? $englishTitle : $arabicTitle,
            ]
        );

        // Save Arabic translation (if primary is English)
        if (!$isArabic) {
            DB::table('ec_product_attribute_sets_translations')->updateOrInsert(
                [
                    'lang_code' => 'ar',
                    'ec_product_attribute_sets_id' => $attributeSet->id,
                ],
                [
                    'title' => $englishTitle,
                ]
            );
        }
    }

    protected function saveAttributeTranslation(
        ProductAttribute $attribute,
        string $arabicTitle,
        string $englishTitle,
        bool $isArabic
    ): void {
        // Save English translation
        DB::table('ec_product_attributes_translations')->updateOrInsert(
            [
                'lang_code' => 'en_US',
                'ec_product_attributes_id' => $attribute->id,
            ],
            [
                'title' => $isArabic ? $englishTitle : $arabicTitle,
            ]
        );

        // Save Arabic translation (if primary is English)
        if (!$isArabic) {
            DB::table('ec_product_attributes_translations')->updateOrInsert(
                [
                    'lang_code' => 'ar',
                    'ec_product_attributes_id' => $attribute->id,
                ],
                [
                    'title' => $englishTitle,
                ]
            );
        }
    }

    protected function processProductVariants(array $productData, Product $mainProduct): void
    {
        if (empty($productData['variants']) || count($productData['variants']) <= 1) {
            return;
        }

        ProductVariation::where('configurable_product_id', $mainProduct->id)->delete();

        foreach ($productData['variants'] as $index => $variant) {
            $variantProduct = $this->createVariantProduct($productData, $variant);
            
            $variation = ProductVariation::create([
                'configurable_product_id' => $mainProduct->id,
                'product_id' => $variantProduct->id,
                'is_default' => $index === 0,
            ]);

            $this->processVariantAttributes($variation, $variant);
        }

        $this->info("Created " . count($productData['variants']) . " variations for: {$mainProduct->name}");
    }

    protected function createVariantProduct(array $productData, array $variant): Product
    {
        $product = Product::updateOrCreate(
            ['shopify_id' => $variant['id']],
            [
                'name' => $productData['title'] . ' - ' . ($variant['title'] ?? 'Variant'),
                'description' => '',
                'status' => 'published',
                'price' => $this->parsePrice($variant['price'] ?? 0),
                'sku' => $variant['sku'] ?? null,
                'barcode' => $variant['barcode'] ?? null,
                'quantity' => $variant['inventory_quantity'] ?? 0,
                'weight' => $variant['weight'] ?? null,
                'product_type' => ProductTypeEnum::PHYSICAL,
                'is_variation' => true,
                'with_storehouse_management' => false,
                'stock_status' => 'in_stock',
            ]
        );

        $this->createSlug($product, $product->name);

        return $product;
    }

    protected function processVariantAttributes(ProductVariation $variation, array $variant): void
    {
        for ($i = 1; $i <= 3; $i++) {
            $optionKey = "option{$i}";

            if (empty($variant[$optionKey])) {
                continue;
            }

            $attribute = ProductAttribute::where('title', $variant[$optionKey])->first();

            if ($attribute) {
                ProductVariationItem::updateOrCreate(
                    [
                        'variation_id' => $variation->id,
                        'attribute_id' => $attribute->id,
                    ]
                );
            }
        }
    }

    protected function processProductImages(array $productData, Product $product): void
    {
        if (empty($productData['images'])) {
            return;
        }

        $imageUrls = [];
        $folderId = $this->ensureProductImagesFolder($product);

        foreach ($productData['images'] as $image) {
            try {
                $mediaModel = MediaFile::where('shopify_id', (string) $image['id'])->first();

                if (!$mediaModel) {
                    $uploadResult = $this->downloadAndStoreImage($image['src'], $folderId);

                    if ($uploadResult && !$uploadResult['error']) {
                        $fileData = $uploadResult['data'];
                        
                        $mediaModel = MediaFile::create([
                            'name' => $fileData['name'],
                            'mime_type' => $fileData['mime_type'],
                            'size' => $fileData['size'],
                            'url' => $fileData['url'],
                            'folder_id' => $folderId,
                            'shopify_id' => (string) $image['id'],
                            'user_id' => 0,
                        ]);
                        
                        $imageUrls[] = $fileData['url'];
                    }
                } else {
                    $imageUrls[] = $mediaModel->url;
                }
            } catch (Exception $e) {
                $this->warn("Failed to process image {$image['id']}: {$e->getMessage()}");
            }
        }

        if (!empty($imageUrls)) {
            $product->update([
                'images' => json_encode($imageUrls),
                'image' => $imageUrls[0],
            ]);
        }
    }

    protected function ensureProductImagesFolder(Product $product): int
    {
        $folder = MediaFolder::updateOrCreate(
            ['name' => (string) $product->id],
            [
                'parent_id' => 10,
                'slug' => 'shopify-products-' . $product->id,
                'user_id' => 0,
            ]
        );

        return $folder->id;
    }

    protected function downloadAndStoreImage(string $imageUrl, int $folderId): ?array
    {
        try {
            set_time_limit(30);

            $response = Http::timeout(20)
                ->connectTimeout(10)
                ->retry(2, 1000)
                ->get($imageUrl);

            if (!$response->successful()) {
                return null;
            }

            $imageData = $response->body();
            $cleanedUrl = preg_replace('/\?.*/', '', $imageUrl);
            $extension = pathinfo($cleanedUrl, PATHINFO_EXTENSION) ?: 'jpg';
            $fileName = Str::slug(pathinfo($cleanedUrl, PATHINFO_FILENAME)) . '-' . Str::random(5) . '.' . $extension;

            $tmpDir = storage_path('app/public/tmp');
            if (!file_exists($tmpDir)) {
                mkdir($tmpDir, 0755, true);
            }

            $storagePath = $tmpDir . '/' . $fileName;
            file_put_contents($storagePath, $imageData);

            try {
                $uploadResult = RvMedia::uploadFromPath($storagePath, $folderId, 'products', $fileName);
                return $uploadResult;
            } finally {
                if (file_exists($storagePath)) {
                    unlink($storagePath);
                }
            }
        } catch (Exception $e) {
            $this->warn("Image download failed for {$imageUrl}: {$e->getMessage()}");
            return null;
        }
    }

    protected function assignImagesToVariations(array $productData): void
    {
        if (empty($productData['variants'])) {
            return;
        }

        foreach ($productData['variants'] as $variant) {
            if (empty($variant['image_id'])) {
                continue;
            }

            $variantProduct = Product::where('shopify_id', $variant['id'])->first();
            $media = MediaFile::where('shopify_id', (string) $variant['image_id'])->first();

            if ($variantProduct && $media) {
                $variantProduct->update(['image' => $media->url]);
            }
        }
    }

    protected function processTags(array $productData, Product $product): void
    {
        if (empty($productData['tags'])) {
            return;
        }

        $tags = is_array($productData['tags'])
            ? $productData['tags']
            : array_map('trim', explode(',', $productData['tags']));

        $tagIds = [];

        foreach ($tags as $tagName) {
            $tagName = trim($tagName);
            
            if (empty($tagName)) {
                continue;
            }

            $tag = Tag::updateOrCreate(
                ['name' => $tagName],
                [
                    'description' => '',
                    'status' => 'published',
                    'author_id' => 0,
                    'author_type' => 'Botble\ACL\Models\User',
                ]
            );

            $tagIds[] = $tag->id;
        }

        if (!empty($tagIds)) {
            $product->tags()->sync($tagIds);
        }
    }

    protected function processProductType(array $productData, Product $product): void
    {
        if (empty($productData['product_type'])) {
            return;
        }

        $productType = trim($productData['product_type']);

        $category = ProductCategory::updateOrCreate(
            ['name' => $productType],
            [
                'description' => '',
                'status' => 'published',
                'is_default' => false,
            ]
        );

        DB::table('ec_product_categories_translations')->UpdateOrInsert(
            [
                'lang_code' => 'en_US',
                'ec_product_categories_id' => $category->id,
            ],
            [
                'name' => $productType,
            ]
        );


      
 
        $product->categories()->syncWithoutDetaching([$category->id]);


          // slug pro product category
        $slug = Slug::updateOrCreate([
            'reference_id' => $category->id,
            'reference_type' => ProductCategory::class,
        ], [
            'key' => Str::slug($category->name),
            'prefix' => 'categories',
        ]);
    }

    protected function isArabicText(string $text): bool
    {
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text) === 1;
    }

    protected function parsePrice($price): ?float
    {
        if (is_null($price)) {
            return null;
        }

        return (float) preg_replace('/[^0-9.]/', '', $price);
    }
}