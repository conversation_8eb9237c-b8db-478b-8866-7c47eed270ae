{"__meta": {"id": "X143f2e28218fc7e030411e41f3975099", "datetime": "2025-11-29 17:08:00", "utime": **********.761461, "method": "POST", "uri": "/ajax/render-ui-blocks", "ip": "127.0.0.1"}, "php": {"version": "8.2.29", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1764436079.899633, "end": **********.761483, "duration": 0.8618500232696533, "duration_str": "862ms", "measures": [{"label": "Booting", "start": 1764436079.899633, "relative_start": 0, "end": **********.392865, "relative_end": **********.392865, "duration": 0.49323201179504395, "duration_str": "493ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.392876, "relative_start": 0.4932429790496826, "end": **********.761486, "relative_end": 3.0994415283203125e-06, "duration": 0.368610143661499, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 56044016, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 59, "templates": [{"name": "1x theme.shofy::partials.shortcodes.ecommerce-products.index", "param_count": null, "params": [], "start": **********.474336, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-products/index.blade.phptheme.shofy::partials.shortcodes.ecommerce-products.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fecommerce-products%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.shortcodes.ecommerce-products.index"}, {"name": "1x theme.shofy::partials.shortcodes.ecommerce-products.grid", "param_count": null, "params": [], "start": **********.477604, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-products/grid.blade.phptheme.shofy::partials.shortcodes.ecommerce-products.grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fecommerce-products%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.shortcodes.ecommerce-products.grid"}, {"name": "1x theme.shofy::partials.section-title", "param_count": null, "params": [], "start": **********.479953, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/section-title.blade.phptheme.shofy::partials.section-title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fsection-title.blade.php&line=1", "ajax": false, "filename": "section-title.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.section-title"}, {"name": "1x theme.shofy::partials.shortcodes.ecommerce-products.partials.sidebar", "param_count": null, "params": [], "start": **********.480781, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-products/partials/sidebar.blade.phptheme.shofy::partials.shortcodes.ecommerce-products.partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fecommerce-products%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.shortcodes.ecommerce-products.partials.sidebar"}, {"name": "2x theme.shofy::partials.shortcodes.ads.includes.item", "param_count": null, "params": [], "start": **********.513432, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ads/includes/item.blade.phptheme.shofy::partials.shortcodes.ads.includes.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fads%2Fincludes%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.shofy::partials.shortcodes.ads.includes.item"}, {"name": "1x theme.shofy::views.ecommerce.includes.product-items", "param_count": null, "params": [], "start": **********.527494, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product-items.blade.phptheme.shofy::views.ecommerce.includes.product-items", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct-items.blade.php&line=1", "ajax": false, "filename": "product-items.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::views.ecommerce.includes.product-items"}, {"name": "6x theme.shofy::views.ecommerce.includes.product-item", "param_count": null, "params": [], "start": **********.53265, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product-item.blade.phptheme.shofy::views.ecommerce.includes.product-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct-item.blade.php&line=1", "ajax": false, "filename": "product-item.blade.php", "line": "?"}, "render_count": 6, "name_original": "theme.shofy::views.ecommerce.includes.product-item"}, {"name": "6x theme.shofy::views.ecommerce.includes.product.style-1.grid", "param_count": null, "params": [], "start": **********.533501, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/grid.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}, "render_count": 6, "name_original": "theme.shofy::views.ecommerce.includes.product.style-1.grid"}, {"name": "6x theme.shofy::views.ecommerce.includes.product.badges", "param_count": null, "params": [], "start": **********.539734, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/badges.blade.phptheme.shofy::views.ecommerce.includes.product.badges", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fbadges.blade.php&line=1", "ajax": false, "filename": "badges.blade.php", "line": "?"}, "render_count": 6, "name_original": "theme.shofy::views.ecommerce.includes.product.badges"}, {"name": "6x theme.shofy::views.ecommerce.includes.product.style-1.actions", "param_count": null, "params": [], "start": **********.560721, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 6, "name_original": "theme.shofy::views.ecommerce.includes.product.style-1.actions"}, {"name": "6x theme.shofy::views.ecommerce.includes.product.style-1.rating", "param_count": null, "params": [], "start": **********.575939, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/rating.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.rating", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Frating.blade.php&line=1", "ajax": false, "filename": "rating.blade.php", "line": "?"}, "render_count": 6, "name_original": "theme.shofy::views.ecommerce.includes.product.style-1.rating"}, {"name": "6x plugins/ecommerce::themes.includes.rating-star", "param_count": null, "params": [], "start": **********.57722, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/rating-star.blade.phpplugins/ecommerce::themes.includes.rating-star", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Frating-star.blade.php&line=1", "ajax": false, "filename": "rating-star.blade.php", "line": "?"}, "render_count": 6, "name_original": "plugins/ecommerce::themes.includes.rating-star"}, {"name": "6x theme.shofy::views.ecommerce.includes.product.style-1.price", "param_count": null, "params": [], "start": **********.579754, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/price.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fprice.blade.php&line=1", "ajax": false, "filename": "price.blade.php", "line": "?"}, "render_count": 6, "name_original": "theme.shofy::views.ecommerce.includes.product.style-1.price"}, {"name": "6x plugins/ecommerce::themes.includes.product-price", "param_count": null, "params": [], "start": **********.580544, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-price.blade.phpplugins/ecommerce::themes.includes.product-price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-price.blade.php&line=1", "ajax": false, "filename": "product-price.blade.php", "line": "?"}, "render_count": 6, "name_original": "plugins/ecommerce::themes.includes.product-price"}, {"name": "4x plugins/ecommerce::themes.includes.product-prices.original", "param_count": null, "params": [], "start": **********.620891, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-prices/original.blade.phpplugins/ecommerce::themes.includes.product-prices.original", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-prices%2Foriginal.blade.php&line=1", "ajax": false, "filename": "original.blade.php", "line": "?"}, "render_count": 4, "name_original": "plugins/ecommerce::themes.includes.product-prices.original"}]}, "route": {"uri": "POST ajax/render-ui-blocks", "middleware": "Botble\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware, web, core, localeSessionRedirect, localizationRedirect", "controller": "Botble\\Shortcode\\Http\\Controllers\\ShortcodeController@ajaxRenderUiBlock", "namespace": null, "prefix": "/", "where": [], "as": "public.ajax.render-ui-block", "file": "<a href=\"phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fshortcode%2Fsrc%2FHttp%2FControllers%2FShortcodeController.php&line=57\" onclick=\"\">platform/packages/shortcode/src/Http/Controllers/ShortcodeController.php:57-74</a>"}, "queries": {"nb_statements": 36, "nb_visible_statements": 36, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.026419999999999996, "accumulated_duration_str": "26.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 352}], "start": **********.430565, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 0, "width_percent": 2.574}, {"sql": "select distinct `ec_products`.*, `products_with_final_price`.`final_price`, (select count(*) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_count`, (select avg(`ec_reviews`.`star`) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_avg` from `ec_products` inner join\n(\nSELECT DISTINCT\nec_products.id,\nCASE\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price <> 0\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 0 AND\nec_products.sale_price = 0\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\n(\nec_products.start_date > '2025-11-29 17:08:00' OR\nec_products.end_date < '2025-11-29 17:08:00'\n)\n) THEN ec_products.price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-11-29 17:08:00' AND\nec_products.end_date >= '2025-11-29 17:08:00'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date IS NULL AND\nec_products.end_date >= '2025-11-29 17:08:00'\n) THEN ec_products.sale_price\nWHEN (\nec_products.sale_type = 1 AND\nec_products.start_date <= '2025-11-29 17:08:00' AND\nec_products.end_date IS NULL\n) THEN ec_products.sale_price\nELSE ec_products.price\nEND AS final_price\nFROM ec_products\n) AS products_with_final_price\non `products_with_final_price`.`id` = `ec_products`.`id` where `status` = 'published' and exists (select * from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_products`.`id` = `ec_product_category_product`.`product_id` and `ec_product_category_product`.`category_id` in ('20')) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) order by `order` asc, `created_at` desc limit 12", "type": "query", "params": [], "bindings": ["published", "published", "published", "20", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\hassan code\\shofy\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 706}, {"index": 18, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 352}, {"index": 23, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}], "start": **********.4407191, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 2.574, "width_percent": 9.69}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (14, 16, 33, 49, 52, 54) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\hassan code\\shofy\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 706}, {"index": 24, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 352}], "start": **********.4465742, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 12.263, "width_percent": 3.028}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (14, 16, 33, 49, 52, 54)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\hassan code\\shofy\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 706}, {"index": 24, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 352}], "start": **********.453689, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 15.291, "width_percent": 3.369}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (14, 16, 33, 49, 52, 54)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\hassan code\\shofy\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 706}, {"index": 22, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 352}, {"index": 27, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}], "start": **********.4575808, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 18.66, "width_percent": 2.877}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (14, 16, 33, 49, 52, 54)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\hassan code\\shofy\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 706}, {"index": 22, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 352}, {"index": 27, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}], "start": **********.462118, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 21.537, "width_percent": 4.126}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` in (1, 4, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\hassan code\\shofy\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 706}, {"index": 24, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 352}], "start": **********.465105, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 25.662, "width_percent": 1.703}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1, 4, 6) and `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store'", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/core/support/src/Repositories/Eloquent/RepositoriesAbstract.php", "file": "D:\\hassan code\\shofy\\platform\\core\\support\\src\\Repositories\\Eloquent\\RepositoriesAbstract.php", "line": 351}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/ProductRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\ProductRepository.php", "line": 706}], "start": **********.467335, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 27.366, "width_percent": 2.422}, {"sql": "select * from `ads` where `id` in ('3', '4') and `status` = 'published'", "type": "query", "params": [], "bindings": ["3", "4", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 380}, {"index": 21, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 22, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 23, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}], "start": **********.470838, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 29.788, "width_percent": 2.536}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 49", "type": "query", "params": [], "bindings": [49], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": "view", "name": "theme.shofy::partials.shortcodes.ecommerce-products.partials.sidebar", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-products/partials/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.484324, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 32.324, "width_percent": 3.142}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\ProductCategory' and `slugs`.`reference_id` = 20 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\ProductCategory", 20], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": "view", "name": "theme.shofy::partials.shortcodes.ecommerce-products.partials.sidebar", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-products/partials/sidebar.blade.php", "line": 20}], "start": **********.494728, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 35.466, "width_percent": 3.293}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`parent_id` = 20 and `ec_product_categories`.`parent_id` is not null and not `id` = 20 and `status` = 'published' order by `order` asc", "type": "query", "params": [], "bindings": [20, 20, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.shofy::partials.shortcodes.ecommerce-products.partials.sidebar", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-products/partials/sidebar.blade.php", "line": 24}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.498575, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 38.759, "width_percent": 2.271}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (21, 22, 23, 24) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": "view", "name": "theme.shofy::partials.shortcodes.ecommerce-products.partials.sidebar", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-products/partials/sidebar.blade.php", "line": 24}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5003798, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 41.03, "width_percent": 1.59}, {"sql": "select * from `ec_product_categories` where not `id` is null and `status` = 'published' and `ec_product_categories`.`parent_id` in (21, 22, 23, 24) order by `order` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": "view", "name": "theme.shofy::partials.shortcodes.ecommerce-products.partials.sidebar", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-products/partials/sidebar.blade.php", "line": 24}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.50303, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 42.619, "width_percent": 2.082}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Botble\\\\Ads\\\\Models\\\\Ads' and `meta_boxes`.`reference_id` = 3 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Botble\\Ads\\Models\\Ads", 3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.shofy::partials.shortcodes.ecommerce-products.partials.sidebar", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-products/partials/sidebar.blade.php", "line": 62}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.516517, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 44.701, "width_percent": 3.217}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Botble\\\\Ads\\\\Models\\\\Ads' and `meta_boxes`.`reference_id` = 4 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Botble\\Ads\\Models\\Ads", 4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.shofy::partials.shortcodes.ecommerce-products.partials.sidebar", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-products/partials/sidebar.blade.php", "line": 62}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5237482, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 47.918, "width_percent": 2.347}, {"sql": "select * from `ec_brands` where `ec_brands`.`id` in (1, 2, 3, 4, 5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product-items", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product-items.blade.php", "line": 7}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.5286899, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 50.265, "width_percent": 2.271}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-11-29' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-11-29", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 25}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": **********.542606, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 52.536, "width_percent": 3.104}, {"sql": "select `ec_products`.*, `ec_flash_sale_products`.`flash_sale_id` as `pivot_flash_sale_id`, `ec_flash_sale_products`.`product_id` as `pivot_product_id`, `ec_flash_sale_products`.`price` as `pivot_price`, `ec_flash_sale_products`.`quantity` as `pivot_quantity`, `ec_flash_sale_products`.`sold` as `pivot_sold` from `ec_products` inner join `ec_flash_sale_products` on `ec_products`.`id` = `ec_flash_sale_products`.`product_id` where `ec_flash_sale_products`.`flash_sale_id` in (1) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 25}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": **********.545471, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 55.64, "width_percent": 4.656}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-11-29 17:08:00' and (`end_date` is null or `end_date` >= '2025-11-29 17:08:00') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-11-29 17:08:00", "2025-11-29 17:08:00", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 116}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.556107, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 60.295, "width_percent": 3.974}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 32 and not `parent_id` = 37 limit 1", "type": "query", "params": [], "bindings": [32, 37], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": **********.565806, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 64.269, "width_percent": 2.914}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 32 limit 1", "type": "query", "params": [], "bindings": [0, 32], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 122}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": **********.568457, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 67.184, "width_percent": 1.628}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 52", "type": "query", "params": [], "bindings": [52], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.596987, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 68.812, "width_percent": 2.687}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 27 limit 1", "type": "query", "params": [], "bindings": [0, 27], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": **********.60049, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 71.499, "width_percent": 2.347}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 54", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.629972, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 73.846, "width_percent": 3.066}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 32 and not `parent_id` = 36 limit 1", "type": "query", "params": [], "bindings": [32, 36], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": **********.632782, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 76.911, "width_percent": 1.514}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 32 limit 1", "type": "query", "params": [], "bindings": [0, 32], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 122}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": **********.635142, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 78.425, "width_percent": 2.347}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 16", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 88}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6637669, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 80.772, "width_percent": 2.725}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 32 and not `parent_id` = 38 limit 1", "type": "query", "params": [], "bindings": [32, 38], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": **********.666269, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 83.497, "width_percent": 1.476}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 32 limit 1", "type": "query", "params": [], "bindings": [0, 32], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 122}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": **********.6683989, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 84.974, "width_percent": 2.157}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 33", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 88}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6963341, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 87.131, "width_percent": 3.671}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 32 and not `parent_id` = 34 limit 1", "type": "query", "params": [], "bindings": [32, 34], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": **********.699284, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 90.802, "width_percent": 1.665}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 32 limit 1", "type": "query", "params": [], "bindings": [0, 32], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 122}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": **********.7014651, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 92.468, "width_percent": 1.855}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 14", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 88}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.732035, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 94.322, "width_percent": 2.725}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 20 and not `parent_id` = 24 limit 1", "type": "query", "params": [], "bindings": [20, 24], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": **********.734588, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 97.048, "width_percent": 1.514}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 20 limit 1", "type": "query", "params": [], "bindings": [0, 20], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 122}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": **********.736417, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 98.562, "width_percent": 1.438}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\ProductCategory": {"value": 33, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCategory.php&line=1", "ajax": false, "filename": "ProductCategory.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Product": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductCollection": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCollection.php&line=1", "ajax": false, "filename": "ProductCollection.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Brand": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Base\\Models\\MetaBox": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductVariation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1", "ajax": false, "filename": "ProductVariation.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductLabel": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductLabel.php&line=1", "ajax": false, "filename": "ProductLabel.php", "line": "?"}}, "Botble\\Ads\\Models\\Ads": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fads%2Fsrc%2FModels%2FAds.php&line=1", "ajax": false, "filename": "Ads.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\FlashSale": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FFlashSale.php&line=1", "ajax": false, "filename": "FlashSale.php", "line": "?"}}}, "count": 93, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/telescope\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "language": "en"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/a0797a95-12f1-4568-b8a4-3994a3d09ccd\" target=\"_blank\">View in Telescope</a>", "path_info": "/ajax/render-ui-blocks", "status_code": "<pre class=sf-dump id=sf-dump-1745797560 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1745797560\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1857586812 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1857586812\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-456738792 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">ecommerce-products</span>\"\n  \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>style</span>\" => \"<span class=sf-dump-str title=\"4 characters\">grid</span>\"\n    \"<span class=sf-dump-key>category_ids</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n    \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n    \"<span class=sf-dump-key>with_sidebar</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n    \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"28 characters\">main/gadgets/gadget-girl.png</span>\"\n    \"<span class=sf-dump-key>action_label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">More Products</span>\"\n    \"<span class=sf-dump-key>ads_ids</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3,4</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-456738792\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-77305415 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">267</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;142&quot;, &quot;Google Chrome&quot;;v=&quot;142&quot;, &quot;Not_A Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/142.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1957 characters\">botble_footprints_cookie=eyJpdiI6ImRFeFg2U3EwYUtoR05hRi9RVWdmN0E9PSIsInZhbHVlIjoiVk55ay9yby9GSzFqbEIzV2tzLzJtQis3OHJPWURuN014SmtndlQ4ZmhwN0JSNWdQMmxXVG9abmdrVGIwcENEdDVxc2tvWWlMK0s1WlBXa29wK1IzZUZWeHV6aGE0MTB3UU1YYy9OK0JYcXgvdUw3WGxVVGVWbVVkcStwbFBIWWEiLCJtYWMiOiJlMmI4MjIyMWM3NGI5OWUxYTU3M2RiMDlkNmIxZDE5ODE3NmIxNDJhNzJjODFiYjllYTk2NjY4NDUzMWZhNGUwIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IkZ6Z1d1ZjZtRlhZdy80MFJxOER2T0E9PSIsInZhbHVlIjoibDhQbEMyOStBTlQ5czkzYWRIMGczRDhRMEFVMWNYOVQ1eU0zZ1hYVlp2VWhGUVZmYlYxTksrS0I4aWI3b0FMTXRDd2sybTRGSFZwRGEzN0JTQzZMWFF0S0wwMndURVhIRm51UFQvVElkSFR6dmlBWjRKMVROamc4dW9HTGJselFDTlZQNC9qeDdNOXJvd0djRmN1anc3UHdSL2p1cE9RZGxROEc0SE15elhQUFJ4Q2Y3ODhycmVoM1EwNUJaQTRCY0VXVzNiUmNnS21GM1hWMnJwYUNXWHlIUFd6cmlYRlBnNnMwY3lpK0MzR21lUjRtSm1NZHRPbmUvR2szQngwdW4wZUhVM1Z4NC9PVGxJdG9pVWdPK2RZTXF4N1lUWjR3ZndTeGVFKzZVTmE0RHI4cWp2akJJaEo4WmZWc0s1Y0kwdWx1TGNEeW45ZEJzbDZNeWtadXJKMi9OOHJTcUZrVEIrRTBRa1RNZjFNUVJyaHpWYzdObHpjNU5zMUtvVlVQUlN6amt1aEcwR25iTWhSdFpYcG1jZGFmS0QyN01JVTdpcmtBeDVSQ1BpaVFxbjhBMVZtNEo2eGxGL2lpcjJiT1JreWdFQ3FkcFZraDg3MUNqU253ZmJGck9aSGc0NzNOVWpOUjBoNzU1VFE9IiwibWFjIjoiMTE0ZjdmM2EzODVjMWUxYzFmYzgwNmZhNWEwYzZjYTQwOTc2ODVkNTk1NDZkZGE0MzAwODc5OGMyNzg4ODc2ZCIsInRhZyI6IiJ9; newsletter_popup=1; XSRF-TOKEN=eyJpdiI6IldCRjIzdkFHb1lxdXNSL1pneW8zeFE9PSIsInZhbHVlIjoiZkpoeTA5bDJGYlpkWHY0eHJTUkRQY21iNkN2UG9lbHhJYkc5czAwc3NmYWpYcnpLMUc5TDIwMkdWK0xTQ2VNLzgyV3gyV3NrYkFLa3F5ekd5L2RtbHpEa1FCY0NqeWQ4NFQ0RlJCeHRiL1ZyaklrSGFFZGp2cmU4SkIrdy9ZN20iLCJtYWMiOiIyZGEyNWY5MTVjNzkxOGRkNGFkMWYyYTgwZjM0YzZiMWVmOTYyYzQ3NDBlYzMzMWJmZDBkY2YwMjQxOTNhMTgwIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IkZ1aURHQjkwTnpjU2gvZHRUdFNCc3c9PSIsInZhbHVlIjoiaklWMDc4V1F5aW5zQ3VuS2VNdjVWTjhIYkdJOXppa2pMYmJhdmtHRVdxa05oYkNia0dxZjVwMkxWNmRHWVA3OFNQeTV0dU8vRE94c0hsb0xIeVVmdFBtWWF3STQwMFR5WEtiY050VWhtY2lzamYxZkJRWDhLNkdLM2NWcE1KcVUiLCJtYWMiOiI3ZDI1NDIyMWZkMzJiZDEzZDliOGI1ZDJiMzY4MmZjYTcyZDJiNzhhMDQ1NDhjYzI5YTNlOGRkZDRmNjQ2NTZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77305415\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1445462820 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5832e034bf28c6f97be3a608bb8c655f40381a49</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"320 characters\">{&quot;footprint&quot;:&quot;5832e034bf28c6f97be3a608bb8c655f40381a49&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;127.0.0.1&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>newsletter_popup</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PMPD3sRy7YrBqKNvMw96LkySo4AQ8DJuqi1a8JCS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1445462820\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1463766050 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 29 Nov 2025 17:08:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii9wa1dsNU5SZWRxdE1ZQk9qcGJDb2c9PSIsInZhbHVlIjoiK0JhS01vanp4WjJvNzRLMWFpV1hMZGxnaXZvSnN4ellWU2NPTWo2aVZ5eExORW5GR3g2Y3ZTVGg1NGZOK1NGSnJ3dkp0L243b1c5N3B0TVFzaWdwNlFDR2JkendNc1Bla2hreWRYMzZLbWdEMzlvMjBFOHNmU3JwOGdvM2kwRi8iLCJtYWMiOiI2OGQ3NmE3ZDNmMjFjMGEyYWY2YmFiYjNmYmQ0OWU5MjM0YTM5MWVkZWZjNjFiMTExNmUxYjRlZTY2NzRkMTU1IiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:08:00 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">botble_session=eyJpdiI6IkR6M1dPakk3YXZETWpRMVQ0UVMwa1E9PSIsInZhbHVlIjoiL09LVzF1MG5Nd2RjU2t5VFpGbjczNG1sY3F0MFZyZFdwN0tkaTVHSjBVNHFMKzdoeUdFem9xeUk4UHhZTUZaTXA1aW9lbkFsM3l6OVd3QWJKWDhkVkp2Syt5UVdQRUJkQTh6MXZUN0NqYXZ1YXRYVjF5MnJZR2dRa0h4SXpyNVgiLCJtYWMiOiIxMzFlYmMzYzA4NjRiMTE1ZTkyYjcxNjY3Y2EyYmI1NjZhMTMwNDkwODMxYmJiZDU0YzI2MzMwODY3M2NhM2M3IiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:08:00 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii9wa1dsNU5SZWRxdE1ZQk9qcGJDb2c9PSIsInZhbHVlIjoiK0JhS01vanp4WjJvNzRLMWFpV1hMZGxnaXZvSnN4ellWU2NPTWo2aVZ5eExORW5GR3g2Y3ZTVGg1NGZOK1NGSnJ3dkp0L243b1c5N3B0TVFzaWdwNlFDR2JkendNc1Bla2hreWRYMzZLbWdEMzlvMjBFOHNmU3JwOGdvM2kwRi8iLCJtYWMiOiI2OGQ3NmE3ZDNmMjFjMGEyYWY2YmFiYjNmYmQ0OWU5MjM0YTM5MWVkZWZjNjFiMTExNmUxYjRlZTY2NzRkMTU1IiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:08:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">botble_session=eyJpdiI6IkR6M1dPakk3YXZETWpRMVQ0UVMwa1E9PSIsInZhbHVlIjoiL09LVzF1MG5Nd2RjU2t5VFpGbjczNG1sY3F0MFZyZFdwN0tkaTVHSjBVNHFMKzdoeUdFem9xeUk4UHhZTUZaTXA1aW9lbkFsM3l6OVd3QWJKWDhkVkp2Syt5UVdQRUJkQTh6MXZUN0NqYXZ1YXRYVjF5MnJZR2dRa0h4SXpyNVgiLCJtYWMiOiIxMzFlYmMzYzA4NjRiMTE1ZTkyYjcxNjY3Y2EyYmI1NjZhMTMwNDkwODMxYmJiZDU0YzI2MzMwODY3M2NhM2M3IiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:08:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1463766050\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1995491586 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/telescope</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1995491586\", {\"maxDepth\":0})</script>\n"}}