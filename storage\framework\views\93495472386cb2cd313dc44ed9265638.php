<?php
    $title ??= $shortcode->title;
    $subtitle ??= $shortcode->subtitle;
?>

<?php if($title || $subtitle): ?>
    <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['tp-section-title-wrapper', $class ?? null]); ?>">
        <?php if($subtitle): ?>
            <span class="tp-section-title-pre">
                <?php echo BaseHelper::clean($subtitle); ?>

            </span>
        <?php endif; ?>
        <?php if($title): ?>
            <h3 class="section-title tp-section-title">
                <?php echo $__env->make(Theme::getThemeNamespace('partials.section-title-inner'), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </h3>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH D:\hassan code\shofy\platform\themes/shofy/partials/section-title.blade.php ENDPATH**/ ?>