<ul <?php echo $options; ?>>
    <?php $__currentLoopData = $menu_nodes->loadMissing('metadata'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li>
            <a href="<?php echo e(url($row->url)); ?>"
               title="<?php echo e($row->title); ?>"
               <?php if($row->target !== '_self'): ?> target="<?php echo e($row->target); ?>" <?php endif; ?>
            >
                <?php echo $row->icon_html; ?>


                <?php echo BaseHelper::clean($row->title); ?>

            </a>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<?php /**PATH D:\hassan code\shofy\platform\themes/shofy/partials/footer/menu.blade.php ENDPATH**/ ?>