APP_NAME="متجر كرز"
# Disable debug mode by changing APP_DEBUG=false when your site is ready for live.
APP_DEBUG=true
APP_ENV=production
# Must change APP_URL to your website URL. Example: APP_URL=http://your-domain.com
APP_URL=https://karaz-store-jo.com
APP_KEY=base64:hMS5VtciEk3t/0Ije8BCRl+AZOvU2gJanbAw5i/LgIs=
LOG_CHANNEL=daily
ASSET_URL=https://karaz-store-jo.com
BROADCAST_DRIVER=log
CACHE_DRIVER=redis
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_CLIENT=predis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Change to your database credentials
DB_CONNECTION=mysql
# If you use Laravel Sail, just change DB_HOST to DB_HOST=mysql
# On some hosting DB_HOST can be localhost instead of 127.0.0.1
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE="karaz_store"
DB_USERNAME="hasan"
DB_PASSWORD=password
DB_STRICT=false

# You should change this value to hide your admin URL (DON'T use special characters). Example: ADMIN_DIR=hello-admin then your admin URL will be
# http://your-domain.com/hello-admin
ADMIN_DIR=admin

CMS_ENABLE_INSTALLER=true

#SCOUT_DRIVER=meilisearch
#MEILISEARCH_HOST=http://meilisearch:7700

#SCOUT_DRIVER=elasticsearch
#ELASTICSEARCH_HOSTS=localhost:9200







SCOUT_DRIVER=elastic
SCOUT_QUEUE=false
ELASTICSEARCH_HOST=127.0.0.1
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_SCHEME=https
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=HjBDA+IaRfMX=dNlyfmi
ELASTICSEARCH_SSL_VERIFICATION=false
ELASTICSEARCH_INDEX=products