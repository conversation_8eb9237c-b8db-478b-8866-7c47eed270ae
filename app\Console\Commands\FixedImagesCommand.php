<?php

namespace App\Console\Commands;

use Botble\Ecommerce\Models\Product;
use Botble\Media\Models\MediaFile;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixedImagesCommand extends Command
{
    protected $signature = 'fixed:images-product';
    protected $description = 'Fixed images product';
    public function handle()
    {
        $products = Product::query()->where('image', '')->get();
        foreach ($products as $product) {
            $media = MediaFile::where('shopify_id', $product->shopify_id)->first();
            if ($media) {
                $product->update(['image' => $media->url]);
            }
        }
    }
}