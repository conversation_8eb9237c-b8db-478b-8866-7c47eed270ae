<?php

namespace Tests\Feature;

use Bo<PERSON>ble\Shortcode\Facades\Shortcode;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AjaxRenderUiBlocksTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Register a test shortcode
        Shortcode::register('test-shortcode', 'Test Shortcode', 'Test Description', function ($shortcode) {
            return '<div class="test-shortcode">Test Content: ' . ($shortcode->title ?? 'Default') . '</div>';
        });
    }

    public function test_ajax_render_ui_block_returns_success()
    {
        $response = $this->postJson('/ajax/render-ui-blocks', [
            'name' => 'test-shortcode',
            'attributes' => [
                'title' => 'Hello World'
            ]
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'error',
            'data'
        ]);
    }

    public function test_ajax_render_ui_block_renders_shortcode_content()
    {
        $response = $this->postJson('/ajax/render-ui-blocks', [
            'name' => 'test-shortcode',
            'attributes' => [
                'title' => 'Custom Title'
            ]
        ]);

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertStringContains('test-shortcode', $data);
        $this->assertStringContains('Custom Title', $data);
    }

    public function test_ajax_render_ui_block_with_invalid_shortcode()
    {
        $response = $this->postJson('/ajax/render-ui-blocks', [
            'name' => 'non-existent-shortcode',
            'attributes' => []
        ]);

        $response->assertStatus(200);
        $this->assertNull($response->json('data'));
    }

    public function test_ajax_render_ui_block_validation()
    {
        // Test missing name parameter
        $response = $this->postJson('/ajax/render-ui-blocks', [
            'attributes' => []
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name']);
    }

    public function test_ajax_render_ui_block_caching()
    {
        // Clear cache first
        \Illuminate\Support\Facades\Cache::flush();

        // First request - should hit database/render
        $start = microtime(true);
        $response1 = $this->postJson('/ajax/render-ui-blocks', [
            'name' => 'test-shortcode',
            'attributes' => [
                'title' => 'Cached Content'
            ]
        ]);
        $time1 = microtime(true) - $start;

        $response1->assertStatus(200);
        $data1 = $response1->json('data');

        // Second request - should hit cache (should be faster)
        $start = microtime(true);
        $response2 = $this->postJson('/ajax/render-ui-blocks', [
            'name' => 'test-shortcode',
            'attributes' => [
                'title' => 'Cached Content'
            ]
        ]);
        $time2 = microtime(true) - $start;

        $response2->assertStatus(200);
        $data2 = $response2->json('data');

        // Data should be identical
        $this->assertEquals($data1, $data2);

        // Second request should be faster (cached)
        $this->assertLessThan($time1, $time2);
    }

    public function test_ajax_render_ui_block_different_attributes_different_cache()
    {
        // Clear cache first
        \Illuminate\Support\Facades\Cache::flush();

        // Request with first set of attributes
        $response1 = $this->postJson('/ajax/render-ui-blocks', [
            'name' => 'test-shortcode',
            'attributes' => [
                'title' => 'First Title'
            ]
        ]);

        // Request with different attributes
        $response2 = $this->postJson('/ajax/render-ui-blocks', [
            'name' => 'test-shortcode',
            'attributes' => [
                'title' => 'Second Title'
            ]
        ]);

        $response1->assertStatus(200);
        $response2->assertStatus(200);

        $data1 = $response1->json('data');
        $data2 = $response2->json('data');

        // Data should be different
        $this->assertNotEquals($data1, $data2);
        $this->assertStringContains('First Title', $data1);
        $this->assertStringContains('Second Title', $data2);
    }

    public function test_ajax_render_ui_block_requires_json_request()
    {
        $response = $this->post('/ajax/render-ui-blocks', [
            'name' => 'test-shortcode',
            'attributes' => []
        ]);

        // Should require JSON request due to RequiresJsonRequestMiddleware
        $response->assertStatus(406);
    }

    public function test_ajax_render_ui_block_with_empty_attributes()
    {
        $response = $this->postJson('/ajax/render-ui-blocks', [
            'name' => 'test-shortcode',
            'attributes' => []
        ]);

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertStringContains('test-shortcode', $data);
        $this->assertStringContains('Default', $data); // Default title
    }
}
