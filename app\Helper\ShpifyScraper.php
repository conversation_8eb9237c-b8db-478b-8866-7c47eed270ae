<?php 
namespace App\Helper;

use <PERSON><PERSON>ble\Ecommerce\Models\ProductCategory;
use Bo<PERSON>ble\Slug\Models\Slug;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Botble\Ecommerce\Models\ProductAttribute;
use Bo<PERSON>ble\Ecommerce\Models\ProductAttributeSet;
use Botble\Ecommerce\Models\ProductVariation;
use Botble\Ecommerce\Models\ProductVariationItem;
use Botble\Media\Facades\RvMedia;
use Botble\Media\Models\MediaFile;
use Botble\Media\Models\MediaFolder;
use Botble\Blog\Models\Tag;
use Botble\Ecommerce\Enums\ProductTypeEnum;
use Botble\Ecommerce\Models\Product;
use Exception;
use Illuminate\Support\Facades\File;
use Illuminate\Database\QueryException;

trait ShpifyScraper
{
    // Cache for attribute sets to avoid repeated lookups
    protected array $attributeSetCache = [];
    protected array $attributeCache = [];
    protected array $translationCache = [];

    public function fetchProductsPage(int $page)
    {
        $apiUrl = "{$this->baseUrl}/products.json?limit={$this->perPage}&page={$page}";
        $this->info("Fetching products from: {$apiUrl}");
        
        return Http::withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            'Cookie' => 'shopify_test_cookie=1;',
        ])->timeout(30)->retry(3, 1000)->get($apiUrl);
    }

    protected function processProduct(array $productData): void
    {
        // Use smaller, focused transactions instead of one large transaction
        try {
            $isArabic = $this->isArabicText($productData['title']);
            $arabicData = null;
            $englishData = null;

            if ($isArabic) {
                $arabicData = $productData;
                $englishData = $this->fetchProductByHandle($productData['handle'], 'en');
            } else {
                $englishData = $productData;
                $arabicData = $this->fetchProductByHandle($productData['handle'], 'ar');
            }

            // Transaction 1: Create/update main product
            $mainProduct = $this->retryOnDeadlock(function() use ($arabicData, $englishData) {
                return DB::transaction(function() use ($arabicData, $englishData) {
                    return $this->createOrUpdateProduct($arabicData, $englishData);
                });
            });

            if (!$mainProduct) {
                return;
            }

            // Process attributes outside transaction (they're shared resources)
            $this->processProductAttributes($arabicData ?? $englishData, $mainProduct);

            // Transaction 2: Handle variants
            $this->retryOnDeadlock(function() use ($arabicData, $englishData, $mainProduct) {
                DB::transaction(function() use ($arabicData, $englishData, $mainProduct) {
                    $this->processProductVariants($arabicData ?? $englishData, $mainProduct);
                });
            });

            // Transaction 3: Handle images
            $this->retryOnDeadlock(function() use ($arabicData, $englishData, $mainProduct) {
                DB::transaction(function() use ($arabicData, $englishData, $mainProduct) {
                    $this->processProductImages($arabicData ?? $englishData, $mainProduct);
                });
            });

            // Assign images to variations (separate, handles potential null)
            $this->assignImagesToVariations($arabicData ?? $englishData);

            // Transaction 4: Tags and categories
            $this->retryOnDeadlock(function() use ($arabicData, $englishData, $mainProduct) {
                DB::transaction(function() use ($arabicData, $englishData, $mainProduct) {
                    $this->processTags($arabicData ?? $englishData, $mainProduct);
                    $this->processProductType($arabicData ?? $englishData, $mainProduct);
                });
            });

        } catch (Exception $e) {
            $this->error("Failed to process product {$productData['id']}: {$e->getMessage()}");
            $this->error("Stack trace: " . $e->getTraceAsString());
        }
    }

    /**
     * Retry a callback on deadlock or lock timeout
     */
    protected function retryOnDeadlock(callable $callback, int $maxAttempts = 1)
    {
        $attempts = 0;
        
        while ($attempts < $maxAttempts) {
            try {
                return $callback();
            } catch (QueryException $e) {
                $attempts++;
                
                // Check if it's a deadlock (1213) or lock timeout (1205)
                if (in_array($e->getCode(), ['40001', 'HY000']) && 
                    (str_contains($e->getMessage(), '1213') || str_contains($e->getMessage(), '1205'))) {
                    
                    if ($attempts >= $maxAttempts) {
                        $this->error("Max retry attempts reached for database operation");
                        throw $e;
                    }
                    
                    // Exponential backoff: 100ms, 200ms, 400ms
                    $waitTime = 100 * pow(2, $attempts - 1);
                    $this->warn("Database lock detected, retrying in {$waitTime}ms (attempt {$attempts}/{$maxAttempts})");
                    usleep($waitTime * 1000);
                    continue;
                }
                
                // If it's not a deadlock/timeout, throw immediately
                throw $e;
            }
        }
    }

    protected function createSlug(Product $product, string $title): void
    {
        $slug = Slug::updateOrCreate(
            [
                'reference_id' => $product->id,
                'reference_type' => Product::class,
            ],
            [
                'key' => Str::slug($product->name),
                'prefix' => 'products',
                'reference_id' => $product->id,
                'reference_type' => Product::class,
            ]
        );
        
        $this->info("Created slug for: {$product->name}");
    }

    protected function createOrUpdateProduct(?array $arabicData, ?array $englishData): ?Product
    {
        $primaryData = $arabicData ?? $englishData;
        
        if (!$primaryData) {
            $this->warn("No product data available");
            return null;
        }

        $firstVariant = $primaryData['variants'][0] ?? [];

        if ((!isset($arabicData['title']) || $arabicData['title'] == '') && 
            (!isset($englishData['title']) || $englishData['title'] == '')) {
            return Product::where('shopify_id', $primaryData['id'])->first();
        }

        $product = Product::updateOrCreate(
            ['shopify_id' => $primaryData['id']],
            [
                'name' => $arabicData['title'] ?? $englishData['title'] ?? 'Untitled',
                'description' => $arabicData['body_html'] ?? '',
                'content' => $arabicData['body_html'] ?? '',
                'status' => $primaryData['status'] ?? 'published',
                'is_featured' => false,
                'price' => $this->parsePrice($firstVariant['price'] ?? 0),
                'sku' => $firstVariant['sku'] ?? null,
                'barcode' => $firstVariant['barcode'] ?? null,
                'quantity' => $firstVariant['inventory_quantity'] ?? 0,
                'weight' => $firstVariant['weight'] ?? null,
                'product_type' => ProductTypeEnum::PHYSICAL,
                'is_variation' => false,
                'with_storehouse_management' => false,
                'stock_status' => 'in_stock',
                'shopify_url' => $this->baseUrl . "/products/" . $primaryData['handle'],
            ]
        );

        $this->info("Created product: {$product->name}");

        if ($englishData && $englishData['title']) {
            DB::table('ec_products_translations')->updateOrInsert(
                [
                    'lang_code' => 'en_US',
                    'ec_products_id' => $product->id,
                ],
                [
                    'name' => $englishData['title'],
                    'description' => $englishData['body_html'] ?? '',
                    'content' => $englishData['body_html'] ?? '',
                ]
            );
            
            $this->info("Created English translation for: {$product->name}");
        }

        $slugTitle = $englishData['title'] ?? $arabicData['title'] ?? $product->name;
        $this->createSlug($product, $slugTitle);

        return $product;
    }

    protected function processProductAttributes(array $productData, Product $mainProduct): void
    {
        if (empty($productData['options'])) {
            return;
        }

        $isArabic = $this->isArabicText($productData['title']);
        $alternateData = $this->fetchProductByHandle($productData['handle'], $isArabic ? 'en' : 'ar');

        foreach ($productData['options'] as $index => $option) {
            // Check cache first
            $cacheKey = Str::slug($option['name']);
            
            if (!isset($this->attributeSetCache[$cacheKey])) {
                // Use firstOrCreate to avoid locks on repeated updateOrCreate
                $attributeSet = $this->retryOnDeadlock(function() use ($option) {
                    return ProductAttributeSet::firstOrCreate(
                        ['title' => $option['name']],
                        [
                            'slug' => Str::slug($option['name']),
                            'status' => 'published',
                            'order' => 0,
                            'display_layout' => 'text',
                            'is_searchable' => true,
                            'is_comparable' => true,
                            'is_use_in_product_listing' => true,
                            'use_image_from_product_variation' => true,
                        ]
                    );
                });
                
                $this->attributeSetCache[$cacheKey] = $attributeSet;
                $this->info("Created/Found attribute set: {$attributeSet->title}");
            } else {
                $attributeSet = $this->attributeSetCache[$cacheKey];
            }

            // Save translations with retry logic
            if ($alternateData && isset($alternateData['options'][$index])) {
                $this->saveAttributeSetTranslation(
                    $attributeSet, 
                    $option['name'], 
                    $alternateData['options'][$index]['name'], 
                    $isArabic
                );
            }

            // Sync without detaching (idempotent operation)
            $mainProduct->productAttributeSets()->syncWithoutDetaching([$attributeSet->id]);

            foreach ($option['values'] as $valueIndex => $value) {
                $attrCacheKey = $attributeSet->id . '_' . Str::slug($value);
                
                if (!isset($this->attributeCache[$attrCacheKey])) {
                    $attribute = $this->retryOnDeadlock(function() use ($value, $attributeSet) {
                        return ProductAttribute::firstOrCreate(
                            [
                                'title' => $value,
                                'attribute_set_id' => $attributeSet->id,
                            ],
                            [
                                'slug' => Str::slug($value),
                                'order' => 0,
                            ]
                        );
                    });
                    
                    $this->attributeCache[$attrCacheKey] = $attribute;
                } else {
                    $attribute = $this->attributeCache[$attrCacheKey];
                }

                if ($alternateData && isset($alternateData['options'][$index]['values'][$valueIndex])) {
                    $this->saveAttributeTranslation(
                        $attribute,
                        $value,
                        $alternateData['options'][$index]['values'][$valueIndex],
                        $isArabic
                    );
                }
            }
        }
    }

    protected function saveAttributeSetTranslation(
        ProductAttributeSet $attributeSet, 
        string $arabicTitle, 
        string $englishTitle, 
        bool $isArabic
    ): void {
        // Check cache to avoid unnecessary updates
        $cacheKey = "attr_set_{$attributeSet->id}_en";
        
        if (!isset($this->translationCache[$cacheKey])) {
            $this->retryOnDeadlock(function() use ($attributeSet, $arabicTitle, $englishTitle, $isArabic, $cacheKey) {
                // Check if translation already exists with correct value
                $existing = DB::table('ec_product_attribute_sets_translations')
                    ->where('lang_code', 'en_US')
                    ->where('ec_product_attribute_sets_id', $attributeSet->id)
                    ->first();

                $newTitle = $isArabic ? $englishTitle : $arabicTitle;
                
                if (!$existing || $existing->title !== $newTitle) {
                    DB::table('ec_product_attribute_sets_translations')->updateOrInsert(
                        [
                            'lang_code' => 'en_US',
                            'ec_product_attribute_sets_id' => $attributeSet->id,
                        ],
                        [
                            'title' => $newTitle,
                        ]
                    );
                }
            });

            $this->translationCache[$cacheKey] = true;
            $this->info("Created attribute set translation: {$attributeSet->title}");
        }

        if (!$isArabic) {
            $arCacheKey = "attr_set_{$attributeSet->id}_ar";
            
            if (!isset($this->translationCache[$arCacheKey])) {
                $this->retryOnDeadlock(function() use ($attributeSet, $englishTitle, $arCacheKey) {
                    $existing = DB::table('ec_product_attribute_sets_translations')
                        ->where('lang_code', 'ar')
                        ->where('ec_product_attribute_sets_id', $attributeSet->id)
                        ->first();

                    if (!$existing || $existing->title !== $englishTitle) {
                        DB::table('ec_product_attribute_sets_translations')->updateOrInsert(
                            [
                                'lang_code' => 'ar',
                                'ec_product_attribute_sets_id' => $attributeSet->id,
                            ],
                            [
                                'title' => $englishTitle,
                            ]
                        );
                    }
                });
                
                $this->translationCache[$arCacheKey] = true;
                $this->info("Created Arabic translation for attribute set: {$attributeSet->title}");
            }
        }
    }

    protected function saveAttributeTranslation(
        ProductAttribute $attribute, 
        string $arabicTitle, 
        string $englishTitle, 
        bool $isArabic
    ): void {
        $cacheKey = "attr_{$attribute->id}_en";
        
        if (!isset($this->translationCache[$cacheKey])) {
            $this->retryOnDeadlock(function() use ($attribute, $arabicTitle, $englishTitle, $isArabic, $cacheKey) {
                $existing = DB::table('ec_product_attributes_translations')
                    ->where('lang_code', 'en_US')
                    ->where('ec_product_attributes_id', $attribute->id)
                    ->first();

                $newTitle = $isArabic ? $englishTitle : $arabicTitle;
                
                if (!$existing || $existing->title !== $newTitle) {
                    DB::table('ec_product_attributes_translations')->updateOrInsert(
                        [
                            'lang_code' => 'en_US',
                            'ec_product_attributes_id' => $attribute->id,
                        ],
                        [
                            'title' => $newTitle,
                        ]
                    );
                }
            });
            
            $this->translationCache[$cacheKey] = true;
        }

        if (!$isArabic) {
            $arCacheKey = "attr_{$attribute->id}_ar";
            
            if (!isset($this->translationCache[$arCacheKey])) {
                $this->retryOnDeadlock(function() use ($attribute, $englishTitle, $arCacheKey) {
                    $existing = DB::table('ec_product_attributes_translations')
                        ->where('lang_code', 'ar')
                        ->where('ec_product_attributes_id', $attribute->id)
                        ->first();

                    if (!$existing || $existing->title !== $englishTitle) {
                        DB::table('ec_product_attributes_translations')->updateOrInsert(
                            [
                                'lang_code' => 'ar',
                                'ec_product_attributes_id' => $attribute->id,
                            ],
                            [
                                'title' => $englishTitle,
                            ]
                        );
                    }
                });
                
                $this->translationCache[$arCacheKey] = true;
            }
        }
    }

    protected function processProductVariants(array $productData, Product $mainProduct): void
    {
        if (empty($productData['variants']) || count($productData['variants']) <= 1) {
            return;
        }

        // Delete existing variations for this product only
        ProductVariation::where('configurable_product_id', $mainProduct->id)->delete();

        if (count($productData['variants']) > 1) {
            foreach ($productData['variants'] as $index => $variant) {
                $variantProduct = $this->createVariantProduct($productData, $variant);
                
                $variation = ProductVariation::create([
                    'configurable_product_id' => $mainProduct->id,
                    'product_id' => $variantProduct->id,
                    'is_default' => $index === 0,
                ]);

                $this->processVariantAttributes($variation, $variant);
            }
        }

        $this->info("Created " . count($productData['variants']) . " variations for: {$mainProduct->name}");
    }

    protected function createVariantProduct(array $productData, array $variant): Product
    {
        $product = Product::updateOrCreate(
            ['shopify_id' => $variant['id']],
            [
                'name' => $productData['title'] . ' - ' . ($variant['title'] ?? 'Variant'),
                'description' => '',
                'status' => 'published',
                'price' => $this->parsePrice($variant['price'] ?? 0),
                'sku' => $variant['sku'] ?? null,
                'barcode' => $variant['barcode'] ?? null,
                'quantity' => $variant['inventory_quantity'] ?? 0,
                'weight' => $variant['weight'] ?? null,
                'product_type' => ProductTypeEnum::PHYSICAL,
                'is_variation' => true,
                'with_storehouse_management' => false,
                'stock_status' => 'in_stock',
            ]
        );

        $this->createSlug($product, $product->name);
        return $product;
    }

    protected function processVariantAttributes(ProductVariation $variation, array $variant): void
    {
        for ($i = 1; $i <= 3; $i++) {
            $optionKey = "option{$i}";
            
            if (empty($variant[$optionKey])) {
                continue;
            }

            $attribute = ProductAttribute::where('title', $variant[$optionKey])->first();
            
            if ($attribute) {
                ProductVariationItem::updateOrCreate([
                    'variation_id' => $variation->id,
                    'attribute_id' => $attribute->id,
                ]);
            }
        }
    }

    protected function processProductImages(array $productData, Product $product): void
    {
        if (empty($productData['images'])) {
            return;
        }

        $imageUrls = [];
        $folderId = $this->ensureProductImagesFolder($product);

        foreach ($productData['images'] as $image) {
            try {
                $mediaModel = MediaFile::where('shopify_id', (string) $image['id'])->first();
                $file_exists = $this->checkIfImageExists($mediaModel);

                $this->info("Image exists: " . ($file_exists ? 'yes' : 'no'));

                // Fixed logic: download if media doesn't exist OR file doesn't exist
                if (!$mediaModel || !$file_exists) {
                    $uploadResult = $this->downloadAndStoreImage($image['src'], $folderId);
                    
                    if ($uploadResult && !$uploadResult['error']) {
                        $fileData = $uploadResult['data'];
                        $imageUrls[] = $fileData['url'];

                        // Create or update media model
                        $mediaModel = MediaFile::updateOrCreate(
                            ['shopify_id' => (string) $image['id']],
                            [
                                'name' => $fileData['name'],
                                'mime_type' => $fileData['mime_type'],
                                'size' => $fileData['size'],
                                'url' => $fileData['url'],
                                'folder_id' => $folderId,
                                'user_id' => 0,
                            ]
                        );
                    }
                } else {
                    // Media exists and file exists
                    $imageUrls[] = $mediaModel->url;
                }
            } catch (Exception $e) {
                $this->error("Failed to process image: {$e->getMessage()}");
                continue;
            }
        }

        if (!empty($imageUrls)) {
            $product->update([
                'images' => json_encode($imageUrls),
                'image' => $imageUrls[0],
            ]);
        }
    }

    protected function ensureProductImagesFolder(Product $product): int
    {
        $folder = MediaFolder::updateOrCreate(
            ['name' => (string) $product->id],
            [
                'parent_id' => 10,
                'slug' => 'shopify-products-' . $product->id,
                'user_id' => 0,
            ]
        );

        $this->info("Folder created: {$folder->name}, folder ID: {$folder->id}");
        return $folder->id;
    }

    protected function checkIfImageExists($mediaModel): bool
    {
        if (is_null($mediaModel)) {
            return false;
        }

        $path = public_path("storage/$mediaModel->url");
        return File::exists($path);
    }

    protected function downloadAndStoreImage(string $imageUrl, int $folderId): ?array
    {
        try {
            set_time_limit(30);
            
            $response = Http::timeout(20)
                ->connectTimeout(10)
                ->retry(2, 1000)
                ->get($imageUrl);

            if (!$response->successful()) {
                return null;
            }

            $imageData = $response->body();
            $cleanedUrl = preg_replace('/\?.*/', '', $imageUrl);
            $extension = pathinfo($cleanedUrl, PATHINFO_EXTENSION) ?: 'jpg';
            $fileName = Str::slug(pathinfo($cleanedUrl, PATHINFO_FILENAME)) . '-' . Str::random(5) . '.' . $extension;

            $tmpDir = storage_path('app/public/tmp');
            if (!file_exists($tmpDir)) {
                mkdir($tmpDir, 0755, true);
            }

            $storagePath = $tmpDir . '/' . $fileName;
            file_put_contents($storagePath, $imageData);

            try {
                $uploadResult = RvMedia::uploadFromPath($storagePath, $folderId, 'products', $fileName);
                return $uploadResult;
            } catch (\Exception $e) {
                $this->warn("Media library processing failed for {$imageUrl}: {$e->getMessage()}");
                return null;
            } finally {
                if (file_exists($storagePath)) {
                    unlink($storagePath);
                }
            }
        } catch (Exception $e) {
            $this->warn("Image download failed for {$imageUrl}: {$e->getMessage()}");
            return null;
        }
    }

    protected function assignImagesToVariations(array $productData): void
    {
        if (empty($productData['variants'])) {
            return;
        }

        foreach ($productData['variants'] as $variant) {
            if (empty($variant['image_id'])) {
                continue;
            }

            $variantProduct = Product::where('shopify_id', $variant['id'])->first();
            $media = MediaFile::where('shopify_id', (string) $variant['image_id'])->first();

            // FIX: Check if both exist before accessing properties
            if ($variantProduct && $media && $media->url) {
                $variantProduct->update(['image' => $media->url]);
            } else {
                $this->warn("Could not assign image to variant {$variant['id']}: " . 
                    ($variantProduct ? 'media not found' : 'variant product not found'));
            }
        }
    }

    protected function processTags(array $productData, Product $product): void
    {
        if (empty($productData['tags'])) {
            return;
        }

        $tags = is_array($productData['tags']) 
            ? $productData['tags'] 
            : array_map('trim', explode(',', $productData['tags']));

        $tagIds = [];

        foreach ($tags as $tagName) {
            $tagName = trim($tagName);
            
            if (empty($tagName)) {
                continue;
            }

            $tag = Tag::updateOrCreate(
                ['name' => $tagName],
                [
                    'description' => $tagName,
                    'status' => 'published',
                    'author_id' => 0,
                    'author_type' => 'Botble\ACL\Models\User',
                ]
            );

            $tagIds[] = $tag->id;
        }

        if (!empty($tagIds)) {
            $product->tags()->sync($tagIds);
        }
    }

    protected function isArabicText(string $text): bool
    {
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text) === 1;
    }

    protected function parsePrice($price): ?float
    {
        if (is_null($price)) {
            return null;
        }
        
        return (float) preg_replace('/[^0-9.]/', '', $price);
    }

    protected function processProductType(array $productData, Product $product): void
    {
        if (empty($productData['product_type'])) {
            return;
        }

        $productType = trim($productData['product_type']);

        $category = ProductCategory::updateOrCreate(
            ['name' => $productType],
            [
                'description' => $productType,
                'status' => 'published',
                'is_default' => false
            ]
        );

        DB::table('ec_product_categories_translations')->updateOrInsert(
            [
                'lang_code' => 'en_US',
                'ec_product_categories_id' => $category->id,
            ],
            [
                'name' => $productType,
            ]
        );

        $product->categories()->syncWithoutDetaching([$category->id]);

        $slug = Slug::updateOrCreate(
            [
                'reference_id' => $category->id,
                'reference_type' => ProductCategory::class,
            ],
            [
                'key' => Str::slug($category->name),
                'prefix' => 'product-categories',
            ]
        );
    }

    protected function fetchProductByHandle(string $handle, string $locale = 'ar')
    {
        if ($locale == 'ar') {
            $apiUrl = "{$this->baseUrl}/{$locale}/products/{$handle}.json";
        } else {
            $apiUrl = "{$this->baseUrl}/products/{$handle}.json";
        }

        $this->info("Fetching {$locale} version for {$handle} from: {$apiUrl}");

        try {
            $response = Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                'Cookie' => 'shopify_test_cookie=1;',
            ])->timeout(30)->retry(3, 1000)->get($apiUrl);

            if ($response->successful()) {
                $this->info("Fetched {$locale} version for {$handle} from: {$apiUrl}");
                return $response->json('product');
            }
        } catch (Exception $e) {
            $apiUrl = "{$this->baseUrl}/products/{$handle}.json";
            
            $response = Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                'Cookie' => 'shopify_test_cookie=1;',
            ])->timeout(30)->retry(3, 1000)->get($apiUrl);

            if ($response->successful()) {
                $this->info("Fetched {$locale} version for {$handle} from: {$apiUrl}");
                return $response->json('product');
            }

            $this->warn("Failed to fetch {$locale} version for {$this->baseUrl}/products/{$handle}: {$e->getMessage()}");
        }

        return null;
    }
}