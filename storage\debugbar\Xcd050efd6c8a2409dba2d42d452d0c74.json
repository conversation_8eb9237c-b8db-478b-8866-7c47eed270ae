{"__meta": {"id": "Xcd050efd6c8a2409dba2d42d452d0c74", "datetime": "2025-11-29 17:15:45", "utime": **********.346255, "method": "GET", "uri": "/ajax/announcements", "ip": "127.0.0.1"}, "php": {"version": "8.2.29", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1764436544.825693, "end": **********.346275, "duration": 0.5205821990966797, "duration_str": "521ms", "measures": [{"label": "Booting", "start": 1764436544.825693, "relative_start": 0, "end": **********.304894, "relative_end": **********.304894, "duration": 0.479201078414917, "duration_str": "479ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.304906, "relative_start": 0.47921299934387207, "end": **********.346277, "relative_end": 1.9073486328125e-06, "duration": 0.04137110710144043, "duration_str": "41.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49235336, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "plugins/announcement::announcements", "param_count": null, "params": [], "start": **********.338349, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/announcement/resources/views/announcements.blade.phpplugins/announcement::announcements", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fannouncement%2Fresources%2Fviews%2Fannouncements.blade.php&line=1", "ajax": false, "filename": "announcements.blade.php", "line": "?"}}, {"name": "plugins/announcement::partials.controls", "param_count": null, "params": [], "start": **********.339603, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/announcement/resources/views/partials/controls.blade.phpplugins/announcement::partials.controls", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fannouncement%2Fresources%2Fviews%2Fpartials%2Fcontrols.blade.php&line=1", "ajax": false, "filename": "controls.blade.php", "line": "?"}}, {"name": "plugins/announcement::partials.item", "param_count": null, "params": [], "start": **********.340132, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/announcement/resources/views/partials/item.blade.phpplugins/announcement::partials.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fannouncement%2Fresources%2Fviews%2Fpartials%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "plugins/announcement::partials.item", "param_count": null, "params": [], "start": **********.34137, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/announcement/resources/views/partials/item.blade.phpplugins/announcement::partials.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fannouncement%2Fresources%2Fviews%2Fpartials%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "plugins/announcement::partials.item", "param_count": null, "params": [], "start": **********.34221, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/announcement/resources/views/partials/item.blade.phpplugins/announcement::partials.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fannouncement%2Fresources%2Fviews%2Fpartials%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}]}, "route": {"uri": "GET ajax/announcements", "middleware": "web, core, localeSessionRedirect, localizationRedirect, Botble\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware", "controller": "ArchiElite\\Announcement\\Http\\Controllers\\PublicController@ajaxGetAnnouncements", "namespace": null, "prefix": "/", "where": [], "as": "public.ajax.announcements", "file": "<a href=\"phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fannouncement%2Fsrc%2FHttp%2FControllers%2FPublicController.php&line=11\" onclick=\"\">platform/plugins/announcement/src/Http/Controllers/PublicController.php:11-16</a>"}, "queries": {"nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00106, "accumulated_duration_str": "1.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `announcements` where 1 = 1 and `is_active` = 1 and (`start_date` is null or `start_date` <= '2025-11-29 17:15:45') and (`end_date` is null or `end_date` >= '2025-11-29 17:15:45') order by RAND()", "type": "query", "params": [], "bindings": [1, "2025-11-29 17:15:45", "2025-11-29 17:15:45"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/announcement/src/AnnouncementHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\announcement\\src\\AnnouncementHelper.php", "line": 59}, {"index": 17, "namespace": null, "name": "platform/plugins/announcement/src/AnnouncementHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\announcement\\src\\AnnouncementHelper.php", "line": 74}, {"index": 18, "namespace": null, "name": "platform/plugins/announcement/src/Http/Controllers/PublicController.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\announcement\\src\\Http\\Controllers\\PublicController.php", "line": 15}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.333808, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"ArchiElite\\Announcement\\Models\\Announcement": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fannouncement%2Fsrc%2FModels%2FAnnouncement.php&line=1", "ajax": false, "filename": "Announcement.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "language": "en"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/a0797d59-f6d6-456e-a91a-ec59a871fbd9\" target=\"_blank\">View in Telescope</a>", "path_info": "/ajax/announcements", "status_code": "<pre class=sf-dump id=sf-dump-1184024616 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1184024616\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-826849738 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-826849738\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-351560569 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-351560569\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-493409001 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/142.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;142&quot;, &quot;Google Chrome&quot;;v=&quot;142&quot;, &quot;Not_A Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1957 characters\">botble_footprints_cookie=eyJpdiI6ImRFeFg2U3EwYUtoR05hRi9RVWdmN0E9PSIsInZhbHVlIjoiVk55ay9yby9GSzFqbEIzV2tzLzJtQis3OHJPWURuN014SmtndlQ4ZmhwN0JSNWdQMmxXVG9abmdrVGIwcENEdDVxc2tvWWlMK0s1WlBXa29wK1IzZUZWeHV6aGE0MTB3UU1YYy9OK0JYcXgvdUw3WGxVVGVWbVVkcStwbFBIWWEiLCJtYWMiOiJlMmI4MjIyMWM3NGI5OWUxYTU3M2RiMDlkNmIxZDE5ODE3NmIxNDJhNzJjODFiYjllYTk2NjY4NDUzMWZhNGUwIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IkZ6Z1d1ZjZtRlhZdy80MFJxOER2T0E9PSIsInZhbHVlIjoibDhQbEMyOStBTlQ5czkzYWRIMGczRDhRMEFVMWNYOVQ1eU0zZ1hYVlp2VWhGUVZmYlYxTksrS0I4aWI3b0FMTXRDd2sybTRGSFZwRGEzN0JTQzZMWFF0S0wwMndURVhIRm51UFQvVElkSFR6dmlBWjRKMVROamc4dW9HTGJselFDTlZQNC9qeDdNOXJvd0djRmN1anc3UHdSL2p1cE9RZGxROEc0SE15elhQUFJ4Q2Y3ODhycmVoM1EwNUJaQTRCY0VXVzNiUmNnS21GM1hWMnJwYUNXWHlIUFd6cmlYRlBnNnMwY3lpK0MzR21lUjRtSm1NZHRPbmUvR2szQngwdW4wZUhVM1Z4NC9PVGxJdG9pVWdPK2RZTXF4N1lUWjR3ZndTeGVFKzZVTmE0RHI4cWp2akJJaEo4WmZWc0s1Y0kwdWx1TGNEeW45ZEJzbDZNeWtadXJKMi9OOHJTcUZrVEIrRTBRa1RNZjFNUVJyaHpWYzdObHpjNU5zMUtvVlVQUlN6amt1aEcwR25iTWhSdFpYcG1jZGFmS0QyN01JVTdpcmtBeDVSQ1BpaVFxbjhBMVZtNEo2eGxGL2lpcjJiT1JreWdFQ3FkcFZraDg3MUNqU253ZmJGck9aSGc0NzNOVWpOUjBoNzU1VFE9IiwibWFjIjoiMTE0ZjdmM2EzODVjMWUxYzFmYzgwNmZhNWEwYzZjYTQwOTc2ODVkNTk1NDZkZGE0MzAwODc5OGMyNzg4ODc2ZCIsInRhZyI6IiJ9; newsletter_popup=1; XSRF-TOKEN=eyJpdiI6IlhYOEFWWU02Uzc2bUZjZjhKWTNUaHc9PSIsInZhbHVlIjoiVHB4SWZmeHlrMmdrUjFVV1d3VWIxbDFkSFVBS29IU0orczZLS3VpczlGbU4zaXZtOE5KNEVxbU9yUmd5YTVnek1JL1FSc1B4SHhLTDhvRjljMjJLZml1SkRDUE0ydnVaN1ZBVHNUdzBleHJ4Y1pJTVFEcnd2TnF1WGVUZlVpaisiLCJtYWMiOiIzZGRkZDhmMzNiOGY1YjRkNDQ1NDFiNTE4MzA0YTE2ZGI2NmMxMTQ2NWUyMGFjMDdhM2MzMWFiYzNlNTRiNmIyIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InJQdndidEtlN2h4cXNEMDV0UWZGTXc9PSIsInZhbHVlIjoiRFcxZTdnMTFpZGh4V1UwYm1GcWErSzhrSkltRVNCRU4xL2tacHpKNGl0WXEyNllPN1ltK21QR2pnYzBuaDFFWDV5dzY2dGZyYXM3YUROY2t2akV4cGV0Qm8vd2x2cUgvQkRXdVAyYkdEVy9xalQvN3lRS0E1TG9NZVdmV05NdEkiLCJtYWMiOiIzNGM5N2RlYjI4ZGQ4ODE2NGIzNzM4YzI5NmU2NWJjYjM5MzM0MzcyNmFkNDBiOWM5ZTU4YTc4YWRiMWY3ZGRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-493409001\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1710300996 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5832e034bf28c6f97be3a608bb8c655f40381a49</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"320 characters\">{&quot;footprint&quot;:&quot;5832e034bf28c6f97be3a608bb8c655f40381a49&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;127.0.0.1&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>newsletter_popup</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PMPD3sRy7YrBqKNvMw96LkySo4AQ8DJuqi1a8JCS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1710300996\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1297558842 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 29 Nov 2025 17:15:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IllQOS9uSlk2WGZsQlF3dExmTDNxTFE9PSIsInZhbHVlIjoiOEVUTlFWRHJKYnU4SFVBdnFDdHp1NUxFdWRhdjVKNjZyclZIb0dlN0J6QS9UZm9ZUTN0NlFpUXYwSjZtcUxXYVJvV3RSdmNHYkpGbnBZRWNpU0h0WDdGRko4aTVuNTNrWlNRT0dGZW11b21DeWdTSEtXN0t1dnh3cGh1RG03U1ciLCJtYWMiOiI3ODdmNGYwOWM5NDhmZjgwMWQxMjJkZDBhZDYxYmI5ODJlNDQzNGNhZGQxZGM1ODU1MjcwMzg3MjRjZTcxMzkxIiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:15:45 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">botble_session=eyJpdiI6InRtcThQSzJWcUExaS9LR0JXZkg4dnc9PSIsInZhbHVlIjoiaEg3eHdLQUJuYmVjUUpILzd5MHZEY1Q0REVoQ0xKeERyM0xLRityUkxKOWdoWEE0Q2hRVUZqYUFWWGZsUE5BazRxRmxhQjJOaGhDRHgwdnJTNlJFcWxFK2RzWldJK3NMdTA1SEZOc3I0ajQ5T3FxUk1WcG1ISk1ucmNXZ0MyeCsiLCJtYWMiOiI1N2Q0YTc4ODk3NzY4YTcwNmVlYzdmNjdmZDMwNGJkNDMxZDZhODZhZjQ4NjM4ZTg3OWIyN2E4OTkxYzA4OTE3IiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:15:45 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IllQOS9uSlk2WGZsQlF3dExmTDNxTFE9PSIsInZhbHVlIjoiOEVUTlFWRHJKYnU4SFVBdnFDdHp1NUxFdWRhdjVKNjZyclZIb0dlN0J6QS9UZm9ZUTN0NlFpUXYwSjZtcUxXYVJvV3RSdmNHYkpGbnBZRWNpU0h0WDdGRko4aTVuNTNrWlNRT0dGZW11b21DeWdTSEtXN0t1dnh3cGh1RG03U1ciLCJtYWMiOiI3ODdmNGYwOWM5NDhmZjgwMWQxMjJkZDBhZDYxYmI5ODJlNDQzNGNhZGQxZGM1ODU1MjcwMzg3MjRjZTcxMzkxIiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:15:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">botble_session=eyJpdiI6InRtcThQSzJWcUExaS9LR0JXZkg4dnc9PSIsInZhbHVlIjoiaEg3eHdLQUJuYmVjUUpILzd5MHZEY1Q0REVoQ0xKeERyM0xLRityUkxKOWdoWEE0Q2hRVUZqYUFWWGZsUE5BazRxRmxhQjJOaGhDRHgwdnJTNlJFcWxFK2RzWldJK3NMdTA1SEZOc3I0ajQ5T3FxUk1WcG1ISk1ucmNXZ0MyeCsiLCJtYWMiOiI1N2Q0YTc4ODk3NzY4YTcwNmVlYzdmNjdmZDMwNGJkNDMxZDZhODZhZjQ4NjM4ZTg3OWIyN2E4OTkxYzA4OTE3IiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:15:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297558842\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1256956070 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256956070\", {\"maxDepth\":0})</script>\n"}}