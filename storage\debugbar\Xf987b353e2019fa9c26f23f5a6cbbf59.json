{"__meta": {"id": "Xf987b353e2019fa9c26f23f5a6cbbf59", "datetime": "2025-11-29 17:08:43", "utime": 1764436123.803488, "method": "GET", "uri": "/ajax/products?limit=8&type=all", "ip": "127.0.0.1"}, "php": {"version": "8.2.29", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1764436123.35478, "end": 1764436123.803505, "duration": 0.44872498512268066, "duration_str": "449ms", "measures": [{"label": "Booting", "start": 1764436123.35478, "relative_start": 0, "end": 1764436123.769115, "relative_end": 1764436123.769115, "duration": 0.4143350124359131, "duration_str": "414ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1764436123.769125, "relative_start": 0.41434502601623535, "end": 1764436123.803507, "relative_end": 2.1457672119140625e-06, "duration": 0.03438210487365723, "duration_str": "34.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49939200, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET ajax/products", "middleware": "Botble\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware, web, core, localeSessionRedirect, localizationRedirect", "controller": "Theme\\Shofy\\Http\\Controllers\\ShofyController@ajaxGetProducts", "as": "public.ajax.products", "namespace": null, "prefix": "/ajax", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fsrc%2FHttp%2FControllers%2FShofyController.php&line=18\" onclick=\"\">platform/themes/shofy/src/Http/Controllers/ShofyController.php:18-96</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "language": "en"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/a0797ad6-bd9f-4b15-bb10-a8a700cfba36\" target=\"_blank\">View in Telescope</a>", "path_info": "/ajax/products", "status_code": "<pre class=sf-dump id=sf-dump-881010031 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-881010031\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1701484673 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">all</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1701484673\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1286426622 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1286426622\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-754565378 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;142&quot;, &quot;Google Chrome&quot;;v=&quot;142&quot;, &quot;Not_A Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1957 characters\">botble_footprints_cookie=eyJpdiI6ImRFeFg2U3EwYUtoR05hRi9RVWdmN0E9PSIsInZhbHVlIjoiVk55ay9yby9GSzFqbEIzV2tzLzJtQis3OHJPWURuN014SmtndlQ4ZmhwN0JSNWdQMmxXVG9abmdrVGIwcENEdDVxc2tvWWlMK0s1WlBXa29wK1IzZUZWeHV6aGE0MTB3UU1YYy9OK0JYcXgvdUw3WGxVVGVWbVVkcStwbFBIWWEiLCJtYWMiOiJlMmI4MjIyMWM3NGI5OWUxYTU3M2RiMDlkNmIxZDE5ODE3NmIxNDJhNzJjODFiYjllYTk2NjY4NDUzMWZhNGUwIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IkZ6Z1d1ZjZtRlhZdy80MFJxOER2T0E9PSIsInZhbHVlIjoibDhQbEMyOStBTlQ5czkzYWRIMGczRDhRMEFVMWNYOVQ1eU0zZ1hYVlp2VWhGUVZmYlYxTksrS0I4aWI3b0FMTXRDd2sybTRGSFZwRGEzN0JTQzZMWFF0S0wwMndURVhIRm51UFQvVElkSFR6dmlBWjRKMVROamc4dW9HTGJselFDTlZQNC9qeDdNOXJvd0djRmN1anc3UHdSL2p1cE9RZGxROEc0SE15elhQUFJ4Q2Y3ODhycmVoM1EwNUJaQTRCY0VXVzNiUmNnS21GM1hWMnJwYUNXWHlIUFd6cmlYRlBnNnMwY3lpK0MzR21lUjRtSm1NZHRPbmUvR2szQngwdW4wZUhVM1Z4NC9PVGxJdG9pVWdPK2RZTXF4N1lUWjR3ZndTeGVFKzZVTmE0RHI4cWp2akJJaEo4WmZWc0s1Y0kwdWx1TGNEeW45ZEJzbDZNeWtadXJKMi9OOHJTcUZrVEIrRTBRa1RNZjFNUVJyaHpWYzdObHpjNU5zMUtvVlVQUlN6amt1aEcwR25iTWhSdFpYcG1jZGFmS0QyN01JVTdpcmtBeDVSQ1BpaVFxbjhBMVZtNEo2eGxGL2lpcjJiT1JreWdFQ3FkcFZraDg3MUNqU253ZmJGck9aSGc0NzNOVWpOUjBoNzU1VFE9IiwibWFjIjoiMTE0ZjdmM2EzODVjMWUxYzFmYzgwNmZhNWEwYzZjYTQwOTc2ODVkNTk1NDZkZGE0MzAwODc5OGMyNzg4ODc2ZCIsInRhZyI6IiJ9; newsletter_popup=1; XSRF-TOKEN=eyJpdiI6ImNIbUdtUXRhU1RESElwQmw1SjNXWEE9PSIsInZhbHVlIjoiemtFaS9JeUxjWVBLd2R0Q1VaY2hrSlpWNXRVazJkUlJUOG9iYitCUDZUc2RXMkVNaWJPSU9FbDkrK0o5dktKRkNLSGFCa2gxdkdFZDhwc0JLVjBOcUwvS0svclQreTB6QVVZaVJKVng3K09IYW1wSUlENGZIdHRxamFDQ2RZRjciLCJtYWMiOiI0YTlhMTgxZmM4ZTZkMjZkNzExNzUzYmQ4NWFkM2I3NGVkODkxOTJjNTAwZWQxNWY0MGE2MjhjYzdmOWM5OTU2IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InJ2VjRwYjVpbk9VbVhzanJSZEVLeXc9PSIsInZhbHVlIjoiTWgzYjVIRjJVaU1QYzdHSG1sSmRaaFJEWWtESzAvdktzUDJKTmpIeFhBbHA0MlZsajRLWkFKMGZuRVM3QWQ3OERGUG8rejNyUnJKeFB4MWw5Vm9EWkNHanozeTFuTURueFhKb3VXYUpOcTAyY0J1aDdQa1JNWm9kdnBkaXVkMEgiLCJtYWMiOiIyYTM0YjA2MDU2MWJlYWQ3OTJjZmQ4YzRmYTgyNjM0MjViZjNhOTZkMzM5NTQzMGQ0M2JiNGJjZjMxOWZkZDE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-754565378\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2123208305 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5832e034bf28c6f97be3a608bb8c655f40381a49</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"320 characters\">{&quot;footprint&quot;:&quot;5832e034bf28c6f97be3a608bb8c655f40381a49&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;127.0.0.1&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>newsletter_popup</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PMPD3sRy7YrBqKNvMw96LkySo4AQ8DJuqi1a8JCS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2123208305\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1158338643 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 29 Nov 2025 17:08:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkJJZ3RYUXQrTm5pNFZ1NHV2YVBFbHc9PSIsInZhbHVlIjoiMmI5bkJFZlpJdEkyWXA5ME5JWU1DOFlsMVFpZ2t1STJBVldvRFpRenJXNlBaUlN2Zk9Eek56YmExSjBzNFVkeEtWZHlLcTNOZ215RVRqeXRBNEZqZVdKY292N2tLSGI5SURDWHZNTlUxM1FXRXVFSTlqckJ0Mmw1NUtSRTR3NWsiLCJtYWMiOiJkNzliYzY4ZjU4MTYxZjE0N2NlYWEyMmM4YTM2ZmM0ZjRjNzUwNTUxMTM1YWI0OGY0MDEwYzdjNzYwYjIwODVhIiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:08:43 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">botble_session=eyJpdiI6IkplN2V0RitMNVlBeS9ueTU0UnlMWEE9PSIsInZhbHVlIjoiVnhMa2dlaWhnTFdEZkZjUlg5WEs1akFHS0FLc2RVZVUyLzcxaCtwaFY3RzBBd1JDMFdLdW9GNDZONFQxWmFBb3JhOTJYdC9RUWR6OS9KUXZKa25yeGlxZUU0Q2pxRXpqRCtMb3o3WExwOENzY1Y5OE00UFc3QThWcW5PMGNvY2QiLCJtYWMiOiJmM2U4NTdhYjczMjRmNzBhN2MwZWY1Y2RjOThiOGJmN2YzY2E0MzZmYjA5Njc0ZDg1NGRjNmRlY2E2ZjUwODczIiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:08:43 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkJJZ3RYUXQrTm5pNFZ1NHV2YVBFbHc9PSIsInZhbHVlIjoiMmI5bkJFZlpJdEkyWXA5ME5JWU1DOFlsMVFpZ2t1STJBVldvRFpRenJXNlBaUlN2Zk9Eek56YmExSjBzNFVkeEtWZHlLcTNOZ215RVRqeXRBNEZqZVdKY292N2tLSGI5SURDWHZNTlUxM1FXRXVFSTlqckJ0Mmw1NUtSRTR3NWsiLCJtYWMiOiJkNzliYzY4ZjU4MTYxZjE0N2NlYWEyMmM4YTM2ZmM0ZjRjNzUwNTUxMTM1YWI0OGY0MDEwYzdjNzYwYjIwODVhIiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:08:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">botble_session=eyJpdiI6IkplN2V0RitMNVlBeS9ueTU0UnlMWEE9PSIsInZhbHVlIjoiVnhMa2dlaWhnTFdEZkZjUlg5WEs1akFHS0FLc2RVZVUyLzcxaCtwaFY3RzBBd1JDMFdLdW9GNDZONFQxWmFBb3JhOTJYdC9RUWR6OS9KUXZKa25yeGlxZUU0Q2pxRXpqRCtMb3o3WExwOENzY1Y5OE00UFc3QThWcW5PMGNvY2QiLCJtYWMiOiJmM2U4NTdhYjczMjRmNzBhN2MwZWY1Y2RjOThiOGJmN2YzY2E0MzZmYjA5Njc0ZDg1NGRjNmRlY2E2ZjUwODczIiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:08:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158338643\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-532622162 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-532622162\", {\"maxDepth\":0})</script>\n"}}