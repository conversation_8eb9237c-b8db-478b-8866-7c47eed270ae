{"__meta": {"id": "X3df9c767d040437a7f7cd3161365fb20", "datetime": "2025-11-29 17:09:11", "utime": 1764436151.07392, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.29", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[17:09:08] LOG.warning: Creation of dynamic property Botble\\Shortcode\\Compilers\\Shortcode::$font_family_of_description is deprecated in D:\\hassan code\\shofy\\storage\\framework\\views\\b564e17e67bd123fad28722fbd244aab.php on line 5", "message_html": null, "is_string": false, "label": "warning", "time": **********.702798, "xdebug_link": null, "collector": "log"}, {"message": "[17:09:08] LOG.warning: Creation of dynamic property Botble\\Shortcode\\Compilers\\Shortcode::$background_color is deprecated in D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php on line 73", "message_html": null, "is_string": false, "label": "warning", "time": **********.763652, "xdebug_link": null, "collector": "log"}, {"message": "[17:09:08] LOG.warning: Creation of dynamic property Botble\\Shortcode\\Compilers\\Shortcode::$show_products_count is deprecated in D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php on line 75", "message_html": null, "is_string": false, "label": "warning", "time": **********.763818, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1764436147.992347, "end": 1764436151.073945, "duration": 3.0815980434417725, "duration_str": "3.08s", "measures": [{"label": "Booting", "start": 1764436147.992347, "relative_start": 0, "end": **********.599797, "relative_end": **********.599797, "duration": 0.607450008392334, "duration_str": "607ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.59982, "relative_start": 0.6074728965759277, "end": 1764436151.073948, "relative_end": 2.86102294921875e-06, "duration": 2.474128007888794, "duration_str": "2.47s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 56624752, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 157, "templates": [{"name": "1x theme.shofy::views.page", "param_count": null, "params": [], "start": **********.673698, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/page.blade.phptheme.shofy::views.page", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fpage.blade.php&line=1", "ajax": false, "filename": "page.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::views.page"}, {"name": "1x theme.shofy::partials.shortcodes.simple-slider.index", "param_count": null, "params": [], "start": **********.695177, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/simple-slider/index.blade.phptheme.shofy::partials.shortcodes.simple-slider.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fsimple-slider%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.shortcodes.simple-slider.index"}, {"name": "1x theme.shofy::partials.shortcodes.simple-slider.style-1", "param_count": null, "params": [], "start": **********.712515, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/simple-slider/style-1.blade.phptheme.shofy::partials.shortcodes.simple-slider.style-1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fsimple-slider%2Fstyle-1.blade.php&line=1", "ajax": false, "filename": "style-1.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.shortcodes.simple-slider.style-1"}, {"name": "3x theme.shofy::partials.shortcodes.simple-slider.includes.image", "param_count": null, "params": [], "start": **********.726491, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/simple-slider/includes/image.blade.phptheme.shofy::partials.shortcodes.simple-slider.includes.image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fsimple-slider%2Fincludes%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 3, "name_original": "theme.shofy::partials.shortcodes.simple-slider.includes.image"}, {"name": "1x theme.shofy::partials.shortcodes.ecommerce-categories.index", "param_count": null, "params": [], "start": **********.764399, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-categories/index.blade.phptheme.shofy::partials.shortcodes.ecommerce-categories.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fecommerce-categories%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.shortcodes.ecommerce-categories.index"}, {"name": "1x theme.shofy::partials.shortcodes.ecommerce-categories.slider", "param_count": null, "params": [], "start": **********.767281, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-categories/slider.blade.phptheme.shofy::partials.shortcodes.ecommerce-categories.slider", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fecommerce-categories%2Fslider.blade.php&line=1", "ajax": false, "filename": "slider.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.shortcodes.ecommerce-categories.slider"}, {"name": "1x theme.shofy::partials.section-title", "param_count": null, "params": [], "start": **********.770132, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/section-title.blade.phptheme.shofy::partials.section-title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fsection-title.blade.php&line=1", "ajax": false, "filename": "section-title.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.section-title"}, {"name": "1x theme.shofy::partials.shortcodes.site-features.index", "param_count": null, "params": [], "start": **********.78722, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/site-features/index.blade.phptheme.shofy::partials.shortcodes.site-features.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fsite-features%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.shortcodes.site-features.index"}, {"name": "1x theme.shofy::partials.shortcodes.site-features.style-1", "param_count": null, "params": [], "start": **********.789908, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/site-features/style-1.blade.phptheme.shofy::partials.shortcodes.site-features.style-1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fsite-features%2Fstyle-1.blade.php&line=1", "ajax": false, "filename": "style-1.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.shortcodes.site-features.style-1"}, {"name": "1x __components::1a65d1071503a2e05e73a706bfebac58", "param_count": null, "params": [], "start": **********.795665, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/1a65d1071503a2e05e73a706bfebac58.blade.php__components::1a65d1071503a2e05e73a706bfebac58", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F1a65d1071503a2e05e73a706bfebac58.blade.php&line=1", "ajax": false, "filename": "1a65d1071503a2e05e73a706bfebac58.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1a65d1071503a2e05e73a706bfebac58"}, {"name": "1x __components::e3de92deb18074b63f585befac7d0965", "param_count": null, "params": [], "start": **********.799372, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/e3de92deb18074b63f585befac7d0965.blade.php__components::e3de92deb18074b63f585befac7d0965", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2Fe3de92deb18074b63f585befac7d0965.blade.php&line=1", "ajax": false, "filename": "e3de92deb18074b63f585befac7d0965.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e3de92deb18074b63f585befac7d0965"}, {"name": "1x __components::bbf20ce5b72ea2c57e1a89503478967d", "param_count": null, "params": [], "start": **********.804115, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/bbf20ce5b72ea2c57e1a89503478967d.blade.php__components::bbf20ce5b72ea2c57e1a89503478967d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2Fbbf20ce5b72ea2c57e1a89503478967d.blade.php&line=1", "ajax": false, "filename": "bbf20ce5b72ea2c57e1a89503478967d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bbf20ce5b72ea2c57e1a89503478967d"}, {"name": "1x __components::77abe61255bd507c4f047d5d7b47f763", "param_count": null, "params": [], "start": **********.808131, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/77abe61255bd507c4f047d5d7b47f763.blade.php__components::77abe61255bd507c4f047d5d7b47f763", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F77abe61255bd507c4f047d5d7b47f763.blade.php&line=1", "ajax": false, "filename": "77abe61255bd507c4f047d5d7b47f763.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::77abe61255bd507c4f047d5d7b47f763"}, {"name": "9x packages/shortcode::partials.lazy-loading-placeholder", "param_count": null, "params": [], "start": **********.811792, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/packages/shortcode/resources/views/partials/lazy-loading-placeholder.blade.phppackages/shortcode::partials.lazy-loading-placeholder", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fshortcode%2Fresources%2Fviews%2Fpartials%2Flazy-loading-placeholder.blade.php&line=1", "ajax": false, "filename": "lazy-loading-placeholder.blade.php", "line": "?"}, "render_count": 9, "name_original": "packages/shortcode::partials.lazy-loading-placeholder"}, {"name": "1x theme.shofy::layouts.full-width", "param_count": null, "params": [], "start": **********.854055, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/layouts/full-width.blade.phptheme.shofy::layouts.full-width", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Flayouts%2Ffull-width.blade.php&line=1", "ajax": false, "filename": "full-width.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::layouts.full-width"}, {"name": "1x theme.shofy::partials.back-to-top", "param_count": null, "params": [], "start": **********.8576, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/back-to-top.blade.phptheme.shofy::partials.back-to-top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fback-to-top.blade.php&line=1", "ajax": false, "filename": "back-to-top.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.back-to-top"}, {"name": "1x theme.shofy::partials.mobile-offcanvas", "param_count": null, "params": [], "start": **********.859744, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/mobile-offcanvas.blade.phptheme.shofy::partials.mobile-offcanvas", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fmobile-offcanvas.blade.php&line=1", "ajax": false, "filename": "mobile-offcanvas.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.mobile-offcanvas"}, {"name": "3x theme.shofy::partials.header.logo", "param_count": null, "params": [], "start": **********.863581, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/header/logo.blade.phptheme.shofy::partials.header.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fheader%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 3, "name_original": "theme.shofy::partials.header.logo"}, {"name": "2x __components::133aa97c11fca0f84f02ebcb9fd067dc", "param_count": null, "params": [], "start": **********.926094, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/133aa97c11fca0f84f02ebcb9fd067dc.blade.php__components::133aa97c11fca0f84f02ebcb9fd067dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F133aa97c11fca0f84f02ebcb9fd067dc.blade.php&line=1", "ajax": false, "filename": "133aa97c11fca0f84f02ebcb9fd067dc.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::133aa97c11fca0f84f02ebcb9fd067dc"}, {"name": "2x theme.shofy::partials.currency-switcher", "param_count": null, "params": [], "start": **********.929601, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/currency-switcher.blade.phptheme.shofy::partials.currency-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fcurrency-switcher.blade.php&line=1", "ajax": false, "filename": "currency-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.shofy::partials.currency-switcher"}, {"name": "2x theme.shofy::partials.language-switcher", "param_count": null, "params": [], "start": **********.93448, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/language-switcher.blade.phptheme.shofy::partials.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.shofy::partials.language-switcher"}, {"name": "1x theme.shofy::partials.navigation-bar", "param_count": null, "params": [], "start": **********.939731, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/navigation-bar.blade.phptheme.shofy::partials.navigation-bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fnavigation-bar.blade.php&line=1", "ajax": false, "filename": "navigation-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.navigation-bar"}, {"name": "1x __components::c50817c59f218fea1b6c19be4db347dd", "param_count": null, "params": [], "start": 1764436149.599842, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/c50817c59f218fea1b6c19be4db347dd.blade.php__components::c50817c59f218fea1b6c19be4db347dd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2Fc50817c59f218fea1b6c19be4db347dd.blade.php&line=1", "ajax": false, "filename": "c50817c59f218fea1b6c19be4db347dd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c50817c59f218fea1b6c19be4db347dd"}, {"name": "3x __components::3cec1c87224222bda738c53f782c5bc1", "param_count": null, "params": [], "start": 1764436149.603527, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/3cec1c87224222bda738c53f782c5bc1.blade.php__components::3cec1c87224222bda738c53f782c5bc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F3cec1c87224222bda738c53f782c5bc1.blade.php&line=1", "ajax": false, "filename": "3cec1c87224222bda738c53f782c5bc1.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::3cec1c87224222bda738c53f782c5bc1"}, {"name": "1x __components::41c347c5d0f75e1f562079f57427149e", "param_count": null, "params": [], "start": 1764436149.607393, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/41c347c5d0f75e1f562079f57427149e.blade.php__components::41c347c5d0f75e1f562079f57427149e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F41c347c5d0f75e1f562079f57427149e.blade.php&line=1", "ajax": false, "filename": "41c347c5d0f75e1f562079f57427149e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::41c347c5d0f75e1f562079f57427149e"}, {"name": "1x __components::f6b8d5c7f3ac3703ebfdef4449133ff4", "param_count": null, "params": [], "start": 1764436149.610567, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php__components::f6b8d5c7f3ac3703ebfdef4449133ff4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2Ff6b8d5c7f3ac3703ebfdef4449133ff4.blade.php&line=1", "ajax": false, "filename": "f6b8d5c7f3ac3703ebfdef4449133ff4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f6b8d5c7f3ac3703ebfdef4449133ff4"}, {"name": "1x theme.shofy::partials.header.search-bar", "param_count": null, "params": [], "start": 1764436149.613812, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/header/search-bar.blade.phptheme.shofy::partials.header.search-bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fheader%2Fsearch-bar.blade.php&line=1", "ajax": false, "filename": "search-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.header.search-bar"}, {"name": "2x ccf469bf367e9d43e8287323c43b8fe7::fronts.ajax-search.input", "param_count": null, "params": [], "start": 1764436149.618852, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/components/fronts/ajax-search/input.blade.phpccf469bf367e9d43e8287323c43b8fe7::fronts.ajax-search.input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fcomponents%2Ffronts%2Fajax-search%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 2, "name_original": "ccf469bf367e9d43e8287323c43b8fe7::fronts.ajax-search.input"}, {"name": "2x ccf469bf367e9d43e8287323c43b8fe7::fronts.ajax-search.index", "param_count": null, "params": [], "start": 1764436149.621973, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/components/fronts/ajax-search/index.blade.phpccf469bf367e9d43e8287323c43b8fe7::fronts.ajax-search.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fcomponents%2Ffronts%2Fajax-search%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "ccf469bf367e9d43e8287323c43b8fe7::fronts.ajax-search.index"}, {"name": "1x theme.shofy::views.ecommerce.includes.quick-view-modal", "param_count": null, "params": [], "start": 1764436149.624554, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/quick-view-modal.blade.phptheme.shofy::views.ecommerce.includes.quick-view-modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fquick-view-modal.blade.php&line=1", "ajax": false, "filename": "quick-view-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::views.ecommerce.includes.quick-view-modal"}, {"name": "1x plugins/ecommerce::themes.includes.quick-shop-modal", "param_count": null, "params": [], "start": 1764436149.627184, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/quick-shop-modal.blade.phpplugins/ecommerce::themes.includes.quick-shop-modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fquick-shop-modal.blade.php&line=1", "ajax": false, "filename": "quick-shop-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::themes.includes.quick-shop-modal"}, {"name": "1x theme.shofy::partials.mini-cart", "param_count": null, "params": [], "start": 1764436149.629462, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/mini-cart.blade.phptheme.shofy::partials.mini-cart", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fmini-cart.blade.php&line=1", "ajax": false, "filename": "mini-cart.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.mini-cart"}, {"name": "1x __components::123a2f76ca745c01f392c79275e8e77b", "param_count": null, "params": [], "start": 1764436149.632759, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/123a2f76ca745c01f392c79275e8e77b.blade.php__components::123a2f76ca745c01f392c79275e8e77b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F123a2f76ca745c01f392c79275e8e77b.blade.php&line=1", "ajax": false, "filename": "123a2f76ca745c01f392c79275e8e77b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::123a2f76ca745c01f392c79275e8e77b"}, {"name": "1x theme.shofy::partials.header", "param_count": null, "params": [], "start": 1764436149.635003, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/header.blade.phptheme.shofy::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.header"}, {"name": "1x theme.shofy::partials.header.styles.header-1", "param_count": null, "params": [], "start": 1764436149.637895, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/header/styles/header-1.blade.phptheme.shofy::partials.header.styles.header-1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fheader%2Fstyles%2Fheader-1.blade.php&line=1", "ajax": false, "filename": "header-1.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.header.styles.header-1"}, {"name": "1x theme.shofy::partials.header.top", "param_count": null, "params": [], "start": 1764436149.955234, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/header/top.blade.phptheme.shofy::partials.header.top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fheader%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.header.top"}, {"name": "6x __components::cb6a36e9f26900eb0350b8e5e727a3a5", "param_count": null, "params": [], "start": 1764436149.963925, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/cb6a36e9f26900eb0350b8e5e727a3a5.blade.php__components::cb6a36e9f26900eb0350b8e5e727a3a5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2Fcb6a36e9f26900eb0350b8e5e727a3a5.blade.php&line=1", "ajax": false, "filename": "cb6a36e9f26900eb0350b8e5e727a3a5.blade.php", "line": "?"}, "render_count": 6, "name_original": "__components::cb6a36e9f26900eb0350b8e5e727a3a5"}, {"name": "1x theme.shofy::partials.header.search-form", "param_count": null, "params": [], "start": 1764436149.971604, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/header/search-form.blade.phptheme.shofy::partials.header.search-form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fheader%2Fsearch-form.blade.php&line=1", "ajax": false, "filename": "search-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.header.search-form"}, {"name": "1x ccf469bf367e9d43e8287323c43b8fe7::fronts.ajax-search.categories-dropdown", "param_count": null, "params": [], "start": 1764436150.566603, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/components/fronts/ajax-search/categories-dropdown.blade.phpccf469bf367e9d43e8287323c43b8fe7::fronts.ajax-search.categories-dropdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fcomponents%2Ffronts%2Fajax-search%2Fcategories-dropdown.blade.php&line=1", "ajax": false, "filename": "categories-dropdown.blade.php", "line": "?"}, "render_count": 1, "name_original": "ccf469bf367e9d43e8287323c43b8fe7::fronts.ajax-search.categories-dropdown"}, {"name": "2x theme.shofy::partials.header.actions", "param_count": null, "params": [], "start": 1764436150.572698, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/header/actions.blade.phptheme.shofy::partials.header.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fheader%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.shofy::partials.header.actions"}, {"name": "5x theme.shofy::partials.main-menu", "param_count": null, "params": [], "start": 1764436150.625603, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/main-menu.blade.phptheme.shofy::partials.main-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fmain-menu.blade.php&line=1", "ajax": false, "filename": "main-menu.blade.php", "line": "?"}, "render_count": 5, "name_original": "theme.shofy::partials.main-menu"}, {"name": "1x __components::a7781df99689693edd2c6c189d767089", "param_count": null, "params": [], "start": 1764436150.691411, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/a7781df99689693edd2c6c189d767089.blade.php__components::a7781df99689693edd2c6c189d767089", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2Fa7781df99689693edd2c6c189d767089.blade.php&line=1", "ajax": false, "filename": "a7781df99689693edd2c6c189d767089.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a7781df99689693edd2c6c189d767089"}, {"name": "1x theme.shofy::partials.header.sticky", "param_count": null, "params": [], "start": 1764436150.693721, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/header/sticky.blade.phptheme.shofy::partials.header.sticky", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fheader%2Fsticky.blade.php&line=1", "ajax": false, "filename": "sticky.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.header.sticky"}, {"name": "1x theme.shofy::partials.header.sticky-data", "param_count": null, "params": [], "start": 1764436150.697998, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/header/sticky-data.blade.phptheme.shofy::partials.header.sticky-data", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fheader%2Fsticky-data.blade.php&line=1", "ajax": false, "filename": "sticky-data.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.header.sticky-data"}, {"name": "1x theme.shofy::partials.breadcrumbs", "param_count": null, "params": [], "start": 1764436150.713112, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/breadcrumbs.blade.phptheme.shofy::partials.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.breadcrumbs"}, {"name": "1x theme.shofy::partials.footer", "param_count": null, "params": [], "start": 1764436150.717143, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/footer.blade.phptheme.shofy::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.footer"}, {"name": "1x theme.shofy::partials.footer.style-1", "param_count": null, "params": [], "start": 1764436150.719594, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/footer/style-1.blade.phptheme.shofy::partials.footer.style-1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Ffooter%2Fstyle-1.blade.php&line=1", "ajax": false, "filename": "style-1.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.footer.style-1"}, {"name": "1x theme.shofy::/../widgets/site-info/templates.frontend", "param_count": null, "params": [], "start": 1764436150.730289, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/////widgets/site-info/templates/frontend.blade.phptheme.shofy::/../widgets/site-info/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fwidgets%2Fsite-info%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::/../widgets/site-info/templates.frontend"}, {"name": "2x __components::0226480071dd0e74b211507465ab2265", "param_count": null, "params": [], "start": 1764436150.736121, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/0226480071dd0e74b211507465ab2265.blade.php__components::0226480071dd0e74b211507465ab2265", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F0226480071dd0e74b211507465ab2265.blade.php&line=1", "ajax": false, "filename": "0226480071dd0e74b211507465ab2265.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::0226480071dd0e74b211507465ab2265"}, {"name": "2x __components::6112de9dcda50c4e5e8edc1897f5a562", "param_count": null, "params": [], "start": 1764436150.740904, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/6112de9dcda50c4e5e8edc1897f5a562.blade.php__components::6112de9dcda50c4e5e8edc1897f5a562", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F6112de9dcda50c4e5e8edc1897f5a562.blade.php&line=1", "ajax": false, "filename": "6112de9dcda50c4e5e8edc1897f5a562.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6112de9dcda50c4e5e8edc1897f5a562"}, {"name": "2x __components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "param_count": null, "params": [], "start": 1764436150.744192, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php__components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php&line=1", "ajax": false, "filename": "3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::3fb0a03fd317b61c1c5c6c5fdfca9fed"}, {"name": "2x __components::d442bc2193ee78d753909ce35435a3ce", "param_count": null, "params": [], "start": 1764436150.748435, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/d442bc2193ee78d753909ce35435a3ce.blade.php__components::d442bc2193ee78d753909ce35435a3ce", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2Fd442bc2193ee78d753909ce35435a3ce.blade.php&line=1", "ajax": false, "filename": "d442bc2193ee78d753909ce35435a3ce.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d442bc2193ee78d753909ce35435a3ce"}, {"name": "2x theme.shofy::/../widgets/custom-menu/templates.frontend", "param_count": null, "params": [], "start": 1764436150.753196, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/////widgets/custom-menu/templates/frontend.blade.phptheme.shofy::/../widgets/custom-menu/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fwidgets%2Fcustom-menu%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.shofy::/../widgets/custom-menu/templates.frontend"}, {"name": "2x theme.shofy::partials.footer.menu", "param_count": null, "params": [], "start": 1764436150.761966, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/footer/menu.blade.phptheme.shofy::partials.footer.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Ffooter%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.shofy::partials.footer.menu"}, {"name": "1x theme.shofy::/../widgets/site-contact/templates.frontend", "param_count": null, "params": [], "start": 1764436150.790576, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/////widgets/site-contact/templates/frontend.blade.phptheme.shofy::/../widgets/site-contact/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fwidgets%2Fsite-contact%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::/../widgets/site-contact/templates.frontend"}, {"name": "4x theme.shofy::/../widgets/product-categories/templates.frontend", "param_count": null, "params": [], "start": 1764436150.806414, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/////widgets/product-categories/templates/frontend.blade.phptheme.shofy::/../widgets/product-categories/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fwidgets%2Fproduct-categories%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 4, "name_original": "theme.shofy::/../widgets/product-categories/templates.frontend"}, {"name": "4x theme.shofy::widgets.product-categories.templates.styles.simple-text", "param_count": null, "params": [], "start": 1764436150.809444, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/widgets/product-categories/templates/styles/simple-text.blade.phptheme.shofy::widgets.product-categories.templates.styles.simple-text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fwidgets%2Fproduct-categories%2Ftemplates%2Fstyles%2Fsimple-text.blade.php&line=1", "ajax": false, "filename": "simple-text.blade.php", "line": "?"}, "render_count": 4, "name_original": "theme.shofy::widgets.product-categories.templates.styles.simple-text"}, {"name": "1x theme.shofy::/../widgets/site-copyright/templates.frontend", "param_count": null, "params": [], "start": 1764436150.89263, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/////widgets/site-copyright/templates/frontend.blade.phptheme.shofy::/../widgets/site-copyright/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fwidgets%2Fsite-copyright%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::/../widgets/site-copyright/templates.frontend"}, {"name": "1x theme.shofy::/../widgets/site-accepted-payments/templates.frontend", "param_count": null, "params": [], "start": 1764436150.897593, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/////widgets/site-accepted-payments/templates/frontend.blade.phptheme.shofy::/../widgets/site-accepted-payments/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fwidgets%2Fsite-accepted-payments%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::/../widgets/site-accepted-payments/templates.frontend"}, {"name": "1x theme.shofy::/../widgets/newsletter/templates.frontend", "param_count": null, "params": [], "start": 1764436150.919212, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/////widgets/newsletter/templates/frontend.blade.phptheme.shofy::/../widgets/newsletter/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fwidgets%2Fnewsletter%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::/../widgets/newsletter/templates.frontend"}, {"name": "2x core/js-validation::bootstrap", "param_count": null, "params": [], "start": 1764436150.935379, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": 1764436150.93972, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php&line=1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "6x core/base::forms.fields.html", "param_count": null, "params": [], "start": 1764436150.942885, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php&line=1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 6, "name_original": "core/base::forms.fields.html"}, {"name": "7x a74ad8dfacd4f985eb3977517615ce25::form.field", "param_count": null, "params": [], "start": 1764436150.946223, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/core/base/resources/views/components/form/field.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 7, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.field"}, {"name": "7x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": 1764436150.950555, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 7, "name_original": "core/base::forms.partials.help-block"}, {"name": "7x core/base::forms.partials.errors", "param_count": null, "params": [], "start": 1764436150.952938, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 7, "name_original": "core/base::forms.partials.errors"}, {"name": "8x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": 1764436150.956573, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 8, "name_original": "core/base::forms.columns.column-span"}, {"name": "1x core/base::forms.fields.email", "param_count": null, "params": [], "start": 1764436150.962221, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/core/base/resources/views/forms/fields/email.blade.phpcore/base::forms.fields.email", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.email"}, {"name": "1x laravel-form-builder::button", "param_count": null, "params": [], "start": 1764436150.970905, "type": "php", "hash": "phpD:\\hassan code\\shofy\\vendor\\botble\\form-builder\\src/../resources/views/button.phplaravel-form-builder::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fvendor%2Fbotble%2Fform-builder%2Fresources%2Fviews%2Fbutton.php&line=1", "ajax": false, "filename": "button.php", "line": "?"}, "render_count": 1, "name_original": "laravel-form-builder::button"}, {"name": "1x theme.shofy::layouts.base", "param_count": null, "params": [], "start": 1764436150.982896, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/layouts/base.blade.phptheme.shofy::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::layouts.base"}, {"name": "1x theme.shofy::partials.header-meta", "param_count": null, "params": [], "start": 1764436150.985458, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/header-meta.blade.phptheme.shofy::partials.header-meta", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fheader-meta.blade.php&line=1", "ajax": false, "filename": "header-meta.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.shofy::partials.header-meta"}, {"name": "1x packages/theme::partials.header", "param_count": null, "params": [], "start": 1764436150.988912, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.header"}, {"name": "1x packages/theme::partials.footer", "param_count": null, "params": [], "start": 1764436151.001219, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.footer"}, {"name": "1x packages/theme::fronts.toast-notification", "param_count": null, "params": [], "start": 1764436151.005293, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/packages/theme/resources/views/fronts/toast-notification.blade.phppackages/theme::fronts.toast-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ffronts%2Ftoast-notification.blade.php&line=1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::fronts.toast-notification"}, {"name": "9x packages/shortcode::partials.lazy-loading-script", "param_count": null, "params": [], "start": 1764436151.009554, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/packages/shortcode/resources/views/partials/lazy-loading-script.blade.phppackages/shortcode::partials.lazy-loading-script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fshortcode%2Fresources%2Fviews%2Fpartials%2Flazy-loading-script.blade.php&line=1", "ajax": false, "filename": "lazy-loading-script.blade.php", "line": "?"}, "render_count": 9, "name_original": "packages/shortcode::partials.lazy-loading-script"}, {"name": "1x plugins/cookie-consent::index", "param_count": null, "params": [], "start": 1764436151.05836, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/cookie-consent/resources/views/index.blade.phpplugins/cookie-consent::index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fcookie-consent%2Fresources%2Fviews%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/cookie-consent::index"}, {"name": "1x plugins/sale-popup::front", "param_count": null, "params": [], "start": 1764436151.067001, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/sale-popup/resources/views/front.blade.phpplugins/sale-popup::front", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fsale-popup%2Fresources%2Fviews%2Ffront.blade.php&line=1", "ajax": false, "filename": "front.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/sale-popup::front"}]}, "route": {"uri": "GET /", "middleware": "web, core, localeSessionRedirect, localizationRedirect", "controller": "Botble\\Theme\\Http\\Controllers\\PublicController@getIndex", "namespace": null, "prefix": "/", "where": [], "as": "public.index", "file": "<a href=\"phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php&line=22\" onclick=\"\">platform/packages/theme/src/Http/Controllers/PublicController.php:22-39</a>"}, "queries": {"nb_statements": 34, "nb_visible_statements": 34, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.029249999999999998, "accumulated_duration_str": "29.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `pages` where (`id` = '1' and `status` = 'published') limit 1", "type": "query", "params": [], "bindings": ["1", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 38}, {"index": 18, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 27}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.650228, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 0, "width_percent": 2.803}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1) and `slugs`.`reference_type` = 'Botble\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Botble\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/page/src/Services/PageService.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\page\\src\\Services\\PageService.php", "line": 38}, {"index": 24, "namespace": null, "name": "platform/packages/theme/src/Http/Controllers/PublicController.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.656422, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 2.803, "width_percent": 2.598}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "platform/themes/shofy/config.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\config.php", "line": 24}], "start": **********.668114, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 5.402, "width_percent": 2.53}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Botble\\\\Page\\\\Models\\\\Page' and `meta_boxes`.`reference_id` = 1 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Botble\\Page\\Models\\Page", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.page", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/page.blade.php", "line": 4}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.6797822, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 7.932, "width_percent": 2.838}, {"sql": "select * from `simple_sliders` where `status` = 'published' and `key` = 'home-slider' limit 1", "type": "query", "params": [], "bindings": ["published", "home-slider"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/simple-slider/src/Providers/HookServiceProvider.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\simple-slider\\src\\Providers\\HookServiceProvider.php", "line": 54}, {"index": 22, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 23, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 24, "namespace": null, "name": "platform/packages/shortcode/src/View/View.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\View\\View.php", "line": 57}], "start": **********.687043, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 10.769, "width_percent": 2.564}, {"sql": "select * from `simple_slider_items` where `simple_slider_items`.`simple_slider_id` = 1 and `simple_slider_items`.`simple_slider_id` is not null order by `simple_slider_items`.`order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/simple-slider/src/Providers/HookServiceProvider.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\simple-slider\\src\\Providers\\HookServiceProvider.php", "line": 56}, {"index": 27, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 28, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}], "start": **********.69072, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 13.333, "width_percent": 3.385}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (1, 2, 3) and `meta_boxes`.`reference_type` = 'Botble\\\\SimpleSlider\\\\Models\\\\SimpleSliderItem'", "type": "query", "params": [], "bindings": ["Botble\\SimpleSlider\\Models\\SimpleSliderItem"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": "view", "name": "theme.shofy::partials.shortcodes.simple-slider.index", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/simple-slider/index.blade.php", "line": 3}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.698962, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 16.718, "width_percent": 2.462}, {"sql": "select `ec_product_categories`.*, (select count(*) from `ec_products` inner join `ec_product_category_product` on `ec_products`.`id` = `ec_product_category_product`.`product_id` where `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` and `is_variation` = 0 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))) as `products_count` from `ec_product_categories` where `id` in ('6', '10', '13', '16', '30') and `status` = 'published' order by `order` asc", "type": "query", "params": [], "bindings": [0, 1, "published", "6", "10", "13", "16", "30", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 67}, {"index": 21, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 22, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 23, "namespace": null, "name": "platform/packages/shortcode/src/View/View.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\View\\View.php", "line": 57}], "start": **********.756523, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 19.179, "width_percent": 6.769}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (6, 10, 13, 16, 30) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 67}, {"index": 27, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 28, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}], "start": **********.7608478, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 25.949, "width_percent": 2.598}, {"sql": "select * from `menus` where `status` = 'published' and exists (select * from `language_meta` where `menus`.`id` = `language_meta`.`reference_id` and `language_meta`.`reference_type` = 'Botble\\\\Menu\\\\Models\\\\Menu' and `lang_meta_code` = 'en_US' and `lang_meta_code` = 'en_US')", "type": "query", "params": [], "bindings": ["published", "Botble\\Menu\\Models\\Menu", "en_US", "en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 231}, {"index": 17, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 214}, {"index": 18, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 178}, {"index": 20, "namespace": "view", "name": "theme.shofy::partials.header.styles.header-1", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/header/styles/header-1.blade.php", "line": 101}], "start": 1764436150.581731, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 28.547, "width_percent": 4.718}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`menu_id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 231}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 214}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 178}], "start": 1764436150.585435, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 33.265, "width_percent": 3.692}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 231}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 214}], "start": 1764436150.592309, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 36.957, "width_percent": 3.214}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 21, 22, 23, 24, 25, 27, 28, 29) and `meta_boxes`.`reference_type` = 'Botble\\\\Menu\\\\Models\\\\MenuNode'", "type": "query", "params": [], "bindings": ["Botble\\Menu\\Models\\MenuNode"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 34, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 231}], "start": 1764436150.596402, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 40.171, "width_percent": 1.744}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41) and `meta_boxes`.`reference_type` = 'Botble\\\\Menu\\\\Models\\\\MenuNode'", "type": "query", "params": [], "bindings": ["Botble\\Menu\\Models\\MenuNode"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 231}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 214}], "start": 1764436150.6074839, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 41.915, "width_percent": 3.179}, {"sql": "select * from `menu_locations` where `menu_locations`.`menu_id` in (1, 2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 231}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 214}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 178}], "start": 1764436150.615013, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 45.094, "width_percent": 2.667}, {"sql": "select * from `pages` where `pages`.`id` in (1, 5, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 274}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 188}, {"index": 26, "namespace": "view", "name": "theme.shofy::partials.header.styles.header-1", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/header/styles/header-1.blade.php", "line": 101}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436150.6205518, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 47.761, "width_percent": 2.598}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1, 5, 6) and `slugs`.`reference_type` = 'Botble\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Botble\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 274}, {"index": 30, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 188}, {"index": 32, "namespace": "view", "name": "theme.shofy::partials.header.styles.header-1", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/header/styles/header-1.blade.php", "line": 101}], "start": 1764436150.622659, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 50.359, "width_percent": 1.607}, {"sql": "select * from `pages` where `pages`.`id` in (2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 274}, {"index": 25, "namespace": "view", "name": "theme.shofy::partials.main-menu", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/main-menu.blade.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436150.641911, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 51.966, "width_percent": 2.359}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (2, 3, 4) and `slugs`.`reference_type` = 'Botble\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Botble\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 274}, {"index": 31, "namespace": "view", "name": "theme.shofy::partials.main-menu", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/main-menu.blade.php", "line": 39}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436150.644061, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 54.325, "width_percent": 1.709}, {"sql": "select * from `pages` where `pages`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 274}, {"index": 25, "namespace": "view", "name": "theme.shofy::partials.main-menu", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/main-menu.blade.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436150.665601, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 56.034, "width_percent": 1.88}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (7) and `slugs`.`reference_type` = 'Botble\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Botble\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 274}, {"index": 31, "namespace": "view", "name": "theme.shofy::partials.main-menu", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/main-menu.blade.php", "line": 39}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436150.66719, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 57.915, "width_percent": 1.504}, {"sql": "select * from `widgets` where (`theme` = 'shofy')", "type": "query", "params": [], "bindings": ["shofy"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/packages/widget/src/WidgetGroupCollection.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\src\\WidgetGroupCollection.php", "line": 92}, {"index": 17, "namespace": null, "name": "platform/packages/widget/src/WidgetGroupCollection.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\src\\WidgetGroupCollection.php", "line": 79}, {"index": 18, "namespace": null, "name": "platform/packages/widget/src/WidgetGroupCollection.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\src\\WidgetGroupCollection.php", "line": 65}, {"index": 20, "namespace": null, "name": "platform/packages/widget/helpers/helpers.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\helpers\\helpers.php", "line": 32}], "start": 1764436150.723084, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 59.419, "width_percent": 3.179}, {"sql": "select * from `pages` where `pages`.`id` in (11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 274}, {"index": 25, "namespace": "view", "name": "theme.shofy::/../widgets/custom-menu/templates.frontend", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/////widgets/custom-menu/templates/frontend.blade.php", "line": 5}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436150.756655, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 62.598, "width_percent": 2.085}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (11) and `slugs`.`reference_type` = 'Botble\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Botble\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 274}, {"index": 31, "namespace": "view", "name": "theme.shofy::/../widgets/custom-menu/templates.frontend", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/////widgets/custom-menu/templates/frontend.blade.php", "line": 5}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436150.758926, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 64.684, "width_percent": 2.085}, {"sql": "select * from `pages` where `pages`.`id` in (9, 10, 8, 6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 274}, {"index": 25, "namespace": "view", "name": "theme.shofy::/../widgets/custom-menu/templates.frontend", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/////widgets/custom-menu/templates/frontend.blade.php", "line": 5}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436150.77535, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 66.769, "width_percent": 2.291}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (6, 8, 9, 10) and `slugs`.`reference_type` = 'Botble\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Botble\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\menu\\src\\Menu.php", "line": 274}, {"index": 31, "namespace": "view", "name": "theme.shofy::/../widgets/custom-menu/templates.frontend", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/////widgets/custom-menu/templates/frontend.blade.php", "line": 5}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436150.777741, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 69.06, "width_percent": 2.085}, {"sql": "select `ec_product_categories`.*, (select count(*) from `ec_products` inner join `ec_product_category_product` on `ec_products`.`id` = `ec_product_category_product`.`product_id` where `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` and `is_variation` = 0 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))) as `products_count` from `ec_product_categories` where `status` = 'published' and `id` in (5, 6, 7, 8, 10, 11, 12) order by `order` asc", "type": "query", "params": [], "bindings": [0, 1, "published", "published", 5, 6, 7, 8, 10, 11, 12], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/themes/shofy/widgets/product-categories/product-categories.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\widgets\\product-categories\\product-categories.php", "line": 62}, {"index": 17, "namespace": null, "name": "platform/packages/widget/src/AbstractWidget.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\src\\AbstractWidget.php", "line": 96}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1764436150.797179, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 71.145, "width_percent": 4.581}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (5, 6, 7, 8, 10, 11, 12) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/themes/shofy/widgets/product-categories/product-categories.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\widgets\\product-categories\\product-categories.php", "line": 62}, {"index": 23, "namespace": null, "name": "platform/packages/widget/src/AbstractWidget.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\src\\AbstractWidget.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": 1764436150.8004699, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 75.726, "width_percent": 2.496}, {"sql": "select `ec_product_categories`.*, (select count(*) from `ec_products` inner join `ec_product_category_product` on `ec_products`.`id` = `ec_product_category_product`.`product_id` where `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` and `is_variation` = 0 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))) as `products_count` from `ec_product_categories` where `status` = 'published' and `id` in (3, 4, 15, 18, 19, 20) order by `order` asc", "type": "query", "params": [], "bindings": [0, 1, "published", "published", 3, 4, 15, 18, 19, 20], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/themes/shofy/widgets/product-categories/product-categories.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\widgets\\product-categories\\product-categories.php", "line": 62}, {"index": 17, "namespace": null, "name": "platform/packages/widget/src/AbstractWidget.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\src\\AbstractWidget.php", "line": 96}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1764436150.8249831, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 78.222, "width_percent": 4.513}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (3, 4, 15, 18, 19, 20) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/themes/shofy/widgets/product-categories/product-categories.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\widgets\\product-categories\\product-categories.php", "line": 62}, {"index": 23, "namespace": null, "name": "platform/packages/widget/src/AbstractWidget.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\src\\AbstractWidget.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": 1764436150.828404, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 82.735, "width_percent": 2.701}, {"sql": "select `ec_product_categories`.*, (select count(*) from `ec_products` inner join `ec_product_category_product` on `ec_products`.`id` = `ec_product_category_product`.`product_id` where `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` and `is_variation` = 0 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))) as `products_count` from `ec_product_categories` where `status` = 'published' and `id` in (11, 12, 13, 14, 15, 16, 17) order by `order` asc", "type": "query", "params": [], "bindings": [0, 1, "published", "published", 11, 12, 13, 14, 15, 16, 17], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/themes/shofy/widgets/product-categories/product-categories.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\widgets\\product-categories\\product-categories.php", "line": 62}, {"index": 17, "namespace": null, "name": "platform/packages/widget/src/AbstractWidget.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\src\\AbstractWidget.php", "line": 96}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1764436150.845922, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 85.436, "width_percent": 4.444}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (11, 12, 13, 14, 15, 16, 17) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/themes/shofy/widgets/product-categories/product-categories.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\widgets\\product-categories\\product-categories.php", "line": 62}, {"index": 23, "namespace": null, "name": "platform/packages/widget/src/AbstractWidget.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\src\\AbstractWidget.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": 1764436150.849324, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 89.88, "width_percent": 2.838}, {"sql": "select `ec_product_categories`.*, (select count(*) from `ec_products` inner join `ec_product_category_product` on `ec_products`.`id` = `ec_product_category_product`.`product_id` where `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` and `is_variation` = 0 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))) as `products_count` from `ec_product_categories` where `status` = 'published' and `id` in (1, 2, 3, 4, 5, 6, 7, 8) order by `order` asc", "type": "query", "params": [], "bindings": [0, 1, "published", "published", 1, 2, 3, 4, 5, 6, 7, 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/themes/shofy/widgets/product-categories/product-categories.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\widgets\\product-categories\\product-categories.php", "line": 62}, {"index": 17, "namespace": null, "name": "platform/packages/widget/src/AbstractWidget.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\src\\AbstractWidget.php", "line": 96}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": 1764436150.867764, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 92.718, "width_percent": 4.957}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1, 2, 3, 4, 5, 6, 7, 8) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/themes/shofy/widgets/product-categories/product-categories.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\widgets\\product-categories\\product-categories.php", "line": 62}, {"index": 23, "namespace": null, "name": "platform/packages/widget/src/AbstractWidget.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\widget\\src\\AbstractWidget.php", "line": 96}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": 1764436150.871203, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 97.675, "width_percent": 2.325}]}, "models": {"data": {"Botble\\Menu\\Models\\MenuNode": {"value": 65, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenuNode.php&line=1", "ajax": false, "filename": "MenuNode.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"value": 46, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductCategory": {"value": 33, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCategory.php&line=1", "ajax": false, "filename": "ProductCategory.php", "line": "?"}}, "Botble\\Widget\\Models\\Widget": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fwidget%2Fsrc%2FModels%2FWidget.php&line=1", "ajax": false, "filename": "Widget.php", "line": "?"}}, "Botble\\Page\\Models\\Page": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Botble\\Base\\Models\\MetaBox": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\SimpleSlider\\Models\\SimpleSliderItem": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fsimple-slider%2Fsrc%2FModels%2FSimpleSliderItem.php&line=1", "ajax": false, "filename": "SimpleSliderItem.php", "line": "?"}}, "Botble\\Menu\\Models\\Menu": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Botble\\SimpleSlider\\Models\\SimpleSlider": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fsimple-slider%2Fsrc%2FModels%2FSimpleSlider.php&line=1", "ajax": false, "filename": "SimpleSlider.php", "line": "?"}}, "Botble\\Menu\\Models\\MenuLocation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenuLocation.php&line=1", "ajax": false, "filename": "MenuLocation.php", "line": "?"}}}, "count": 201, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "language": "en"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/a0797b00-5be1-4958-a9e6-b59ed907533a\" target=\"_blank\">View in Telescope</a>", "path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-651639021 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-651639021\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-342870469 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-342870469\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1761840760 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1761840760\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-25357601 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;142&quot;, &quot;Google Chrome&quot;;v=&quot;142&quot;, &quot;Not_A Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/142.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1957 characters\">botble_footprints_cookie=eyJpdiI6ImRFeFg2U3EwYUtoR05hRi9RVWdmN0E9PSIsInZhbHVlIjoiVk55ay9yby9GSzFqbEIzV2tzLzJtQis3OHJPWURuN014SmtndlQ4ZmhwN0JSNWdQMmxXVG9abmdrVGIwcENEdDVxc2tvWWlMK0s1WlBXa29wK1IzZUZWeHV6aGE0MTB3UU1YYy9OK0JYcXgvdUw3WGxVVGVWbVVkcStwbFBIWWEiLCJtYWMiOiJlMmI4MjIyMWM3NGI5OWUxYTU3M2RiMDlkNmIxZDE5ODE3NmIxNDJhNzJjODFiYjllYTk2NjY4NDUzMWZhNGUwIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IkZ6Z1d1ZjZtRlhZdy80MFJxOER2T0E9PSIsInZhbHVlIjoibDhQbEMyOStBTlQ5czkzYWRIMGczRDhRMEFVMWNYOVQ1eU0zZ1hYVlp2VWhGUVZmYlYxTksrS0I4aWI3b0FMTXRDd2sybTRGSFZwRGEzN0JTQzZMWFF0S0wwMndURVhIRm51UFQvVElkSFR6dmlBWjRKMVROamc4dW9HTGJselFDTlZQNC9qeDdNOXJvd0djRmN1anc3UHdSL2p1cE9RZGxROEc0SE15elhQUFJ4Q2Y3ODhycmVoM1EwNUJaQTRCY0VXVzNiUmNnS21GM1hWMnJwYUNXWHlIUFd6cmlYRlBnNnMwY3lpK0MzR21lUjRtSm1NZHRPbmUvR2szQngwdW4wZUhVM1Z4NC9PVGxJdG9pVWdPK2RZTXF4N1lUWjR3ZndTeGVFKzZVTmE0RHI4cWp2akJJaEo4WmZWc0s1Y0kwdWx1TGNEeW45ZEJzbDZNeWtadXJKMi9OOHJTcUZrVEIrRTBRa1RNZjFNUVJyaHpWYzdObHpjNU5zMUtvVlVQUlN6amt1aEcwR25iTWhSdFpYcG1jZGFmS0QyN01JVTdpcmtBeDVSQ1BpaVFxbjhBMVZtNEo2eGxGL2lpcjJiT1JreWdFQ3FkcFZraDg3MUNqU253ZmJGck9aSGc0NzNOVWpOUjBoNzU1VFE9IiwibWFjIjoiMTE0ZjdmM2EzODVjMWUxYzFmYzgwNmZhNWEwYzZjYTQwOTc2ODVkNTk1NDZkZGE0MzAwODc5OGMyNzg4ODc2ZCIsInRhZyI6IiJ9; newsletter_popup=1; XSRF-TOKEN=eyJpdiI6ImNtd2R1NldOaWlzeGFrMHR3OXNNN3c9PSIsInZhbHVlIjoiRmMzN1l2ZFJkaWFzWW5oWFppNnJYN1V6eWR3RERpb3pQczRCbjhNU29uNldjM2hKMnlsbHNCSVFYek4vZ2NJVWV5NVV3UHRzMUVDNzZGck10R2pGYXE4U0dyM0FiOHcwSFBEZFhORzFhb1dUMU5NMDVzOHArWCtLT0dhbEw5akkiLCJtYWMiOiIwNzk0NjZlY2ZlNDM0OTAyMThlYzEwYzBmODJkYWIxYzM3YWIwMjZkMmIxMzE2ODEzZDlkZWE4ZmIyNDU3ZGNiIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IjRiSzZBSGt1L09JQWlJMWlVY211T2c9PSIsInZhbHVlIjoiOThBMzRQZHQ3UGkzVHlvZHRrc3VmQmhwWDZkN2FrWWVQMXZZeTcyenhFZmhiTmtVTEdOUmdqc1BBaXN3Q21YSWxraTlaZjZNVkhjMVR3cTluemVsMVMraFlSVjlMSWUvSVJUdnZmOHpCQ2dwbXkyQnB1OUQ0bDR4T01zeXp0bFQiLCJtYWMiOiIyNDY3NWMwYjk4M2Y2YWQzNTM0YTNlZGIwMDM2YzgyYWVlNzA0ZDIzZTg2ZTEzNDc3ZjhjNmQ5YmYzMTc4YWU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25357601\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-18282556 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5832e034bf28c6f97be3a608bb8c655f40381a49</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"320 characters\">{&quot;footprint&quot;:&quot;5832e034bf28c6f97be3a608bb8c655f40381a49&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;127.0.0.1&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>newsletter_popup</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PMPD3sRy7YrBqKNvMw96LkySo4AQ8DJuqi1a8JCS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18282556\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-387236001 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 29 Nov 2025 17:09:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cms-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.4.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization-at</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>activated-license</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">No</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkdsUEx4ellQOFFZdTVScFZCUWdHUUE9PSIsInZhbHVlIjoibi9Va0Z1TncxRUc4KzByRjBiaUY2NTBRVUFKcTFvNHV6eHFmOWF0NkVKWTEveUlUVlVJd0Z0NzVhanRhbVN5K3lZYXlmRHg5Nmt5Z3BtQzlTdE5oWnRiVFdZc1ZBMDBSdk85aG4wVEYyQzc2aXIyQ1RZWHRnaElVYVhzTUpjaGQiLCJtYWMiOiIyMDZlMTk3MGQ0ZGEzMDM4NzBjODZkNTk1MjY1M2QxOTEwNWEzYzJlODUzOGM0ZmI5OWQ4ZjQ3YjZlNGJlYjlkIiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:09:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">botble_session=eyJpdiI6IlZ6WW1uK1o2RExvNTIrSjhlcmh2dHc9PSIsInZhbHVlIjoiTzl5RWQ0bUlUL3hvbXVTWFc3QWt5VkdOV284N21UQzd0QkNsZmRtWDRoOXJKd0JZMGtud3ppU3FTbktuNlg3ZjlZaGQyanVJbXpBK3NHYWxVclY2cGlIbTZDMERodDI4WmFqUitHNy9RNG1nemlsU3BmZXR6a1NsWHF3RGE2cFkiLCJtYWMiOiJhZjIzZTMyZDMzNDhiZjg3NmI1ZGIwODcyZTQ3YjdiNTIwNzYxN2RjYzhhMGJkNTY3MjJiMGViZGQ0MGZkZTFhIiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:09:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkdsUEx4ellQOFFZdTVScFZCUWdHUUE9PSIsInZhbHVlIjoibi9Va0Z1TncxRUc4KzByRjBiaUY2NTBRVUFKcTFvNHV6eHFmOWF0NkVKWTEveUlUVlVJd0Z0NzVhanRhbVN5K3lZYXlmRHg5Nmt5Z3BtQzlTdE5oWnRiVFdZc1ZBMDBSdk85aG4wVEYyQzc2aXIyQ1RZWHRnaElVYVhzTUpjaGQiLCJtYWMiOiIyMDZlMTk3MGQ0ZGEzMDM4NzBjODZkNTk1MjY1M2QxOTEwNWEzYzJlODUzOGM0ZmI5OWQ4ZjQ3YjZlNGJlYjlkIiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:09:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">botble_session=eyJpdiI6IlZ6WW1uK1o2RExvNTIrSjhlcmh2dHc9PSIsInZhbHVlIjoiTzl5RWQ0bUlUL3hvbXVTWFc3QWt5VkdOV284N21UQzd0QkNsZmRtWDRoOXJKd0JZMGtud3ppU3FTbktuNlg3ZjlZaGQyanVJbXpBK3NHYWxVclY2cGlIbTZDMERodDI4WmFqUitHNy9RNG1nemlsU3BmZXR6a1NsWHF3RGE2cFkiLCJtYWMiOiJhZjIzZTMyZDMzNDhiZjg3NmI1ZGIwODcyZTQ3YjdiNTIwNzYxN2RjYzhhMGJkNTY3MjJiMGViZGQ0MGZkZTFhIiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:09:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387236001\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-650302013 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-650302013\", {\"maxDepth\":0})</script>\n"}}