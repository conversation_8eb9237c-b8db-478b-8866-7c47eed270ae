{"__meta": {"id": "Xc2949e1d342f4479965f4af8798e4d60", "datetime": "2025-11-29 17:14:36", "utime": 1764436476.09621, "method": "POST", "uri": "/ajax/render-ui-blocks", "ip": "127.0.0.1"}, "php": {"version": "8.2.29", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1764436475.189204, "end": 1764436476.096241, "duration": 0.9070370197296143, "duration_str": "907ms", "measures": [{"label": "Booting", "start": 1764436475.189204, "relative_start": 0, "end": 1764436475.764868, "relative_end": 1764436475.764868, "duration": 0.5756640434265137, "duration_str": "576ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1764436475.764879, "relative_start": 0.5756750106811523, "end": 1764436476.096245, "relative_end": 4.0531158447265625e-06, "duration": 0.33136606216430664, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 55296888, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 44, "templates": [{"name": "theme.shofy::partials.shortcodes.ecommerce-flash-sale.index", "param_count": null, "params": [], "start": 1764436475.853792, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-flash-sale/index.blade.phptheme.shofy::partials.shortcodes.ecommerce-flash-sale.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fecommerce-flash-sale%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.shortcodes.ecommerce-flash-sale.style-1", "param_count": null, "params": [], "start": 1764436475.854684, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-flash-sale/style-1.blade.phptheme.shofy::partials.shortcodes.ecommerce-flash-sale.style-1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fecommerce-flash-sale%2Fstyle-1.blade.php&line=1", "ajax": false, "filename": "style-1.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.section-title", "param_count": null, "params": [], "start": 1764436475.855489, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/section-title.blade.phptheme.shofy::partials.section-title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fsection-title.blade.php&line=1", "ajax": false, "filename": "section-title.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.section-title-inner", "param_count": null, "params": [], "start": 1764436475.856184, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/section-title-inner.blade.phptheme.shofy::partials.section-title-inner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fsection-title-inner.blade.php&line=1", "ajax": false, "filename": "section-title-inner.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product-item", "param_count": null, "params": [], "start": 1764436475.858616, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product-item.blade.phptheme.shofy::views.ecommerce.includes.product-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct-item.blade.php&line=1", "ajax": false, "filename": "product-item.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.grid", "param_count": null, "params": [], "start": 1764436475.860273, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/grid.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.badges", "param_count": null, "params": [], "start": 1764436475.876066, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/badges.blade.phptheme.shofy::views.ecommerce.includes.product.badges", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fbadges.blade.php&line=1", "ajax": false, "filename": "badges.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "param_count": null, "params": [], "start": 1764436475.900107, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.rating", "param_count": null, "params": [], "start": 1764436475.929288, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/rating.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.rating", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Frating.blade.php&line=1", "ajax": false, "filename": "rating.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.rating-star", "param_count": null, "params": [], "start": 1764436475.931385, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/rating-star.blade.phpplugins/ecommerce::themes.includes.rating-star", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Frating-star.blade.php&line=1", "ajax": false, "filename": "rating-star.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.price", "param_count": null, "params": [], "start": 1764436475.933827, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/price.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fprice.blade.php&line=1", "ajax": false, "filename": "price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-price", "param_count": null, "params": [], "start": 1764436475.935039, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-price.blade.phpplugins/ecommerce::themes.includes.product-price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-price.blade.php&line=1", "ajax": false, "filename": "product-price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-prices.original", "param_count": null, "params": [], "start": 1764436475.946133, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-prices/original.blade.phpplugins/ecommerce::themes.includes.product-prices.original", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-prices%2Foriginal.blade.php&line=1", "ajax": false, "filename": "original.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.countdown", "param_count": null, "params": [], "start": 1764436475.948145, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/countdown.blade.phptheme.shofy::views.ecommerce.includes.product.countdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fcountdown.blade.php&line=1", "ajax": false, "filename": "countdown.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product-item", "param_count": null, "params": [], "start": 1764436475.949542, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product-item.blade.phptheme.shofy::views.ecommerce.includes.product-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct-item.blade.php&line=1", "ajax": false, "filename": "product-item.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.grid", "param_count": null, "params": [], "start": 1764436475.950952, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/grid.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.badges", "param_count": null, "params": [], "start": 1764436475.954305, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/badges.blade.phptheme.shofy::views.ecommerce.includes.product.badges", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fbadges.blade.php&line=1", "ajax": false, "filename": "badges.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "param_count": null, "params": [], "start": 1764436475.95936, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.rating", "param_count": null, "params": [], "start": 1764436475.979959, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/rating.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.rating", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Frating.blade.php&line=1", "ajax": false, "filename": "rating.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.rating-star", "param_count": null, "params": [], "start": 1764436475.981506, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/rating-star.blade.phpplugins/ecommerce::themes.includes.rating-star", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Frating-star.blade.php&line=1", "ajax": false, "filename": "rating-star.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.price", "param_count": null, "params": [], "start": 1764436475.984073, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/price.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fprice.blade.php&line=1", "ajax": false, "filename": "price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-price", "param_count": null, "params": [], "start": 1764436475.985295, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-price.blade.phpplugins/ecommerce::themes.includes.product-price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-price.blade.php&line=1", "ajax": false, "filename": "product-price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-prices.original", "param_count": null, "params": [], "start": 1764436475.992842, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-prices/original.blade.phpplugins/ecommerce::themes.includes.product-prices.original", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-prices%2Foriginal.blade.php&line=1", "ajax": false, "filename": "original.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.countdown", "param_count": null, "params": [], "start": 1764436475.994258, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/countdown.blade.phptheme.shofy::views.ecommerce.includes.product.countdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fcountdown.blade.php&line=1", "ajax": false, "filename": "countdown.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product-item", "param_count": null, "params": [], "start": 1764436475.995035, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product-item.blade.phptheme.shofy::views.ecommerce.includes.product-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct-item.blade.php&line=1", "ajax": false, "filename": "product-item.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.grid", "param_count": null, "params": [], "start": 1764436475.995968, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/grid.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.badges", "param_count": null, "params": [], "start": 1764436475.999766, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/badges.blade.phptheme.shofy::views.ecommerce.includes.product.badges", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fbadges.blade.php&line=1", "ajax": false, "filename": "badges.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "param_count": null, "params": [], "start": 1764436476.001514, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.rating", "param_count": null, "params": [], "start": 1764436476.02522, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/rating.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.rating", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Frating.blade.php&line=1", "ajax": false, "filename": "rating.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.rating-star", "param_count": null, "params": [], "start": 1764436476.027197, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/rating-star.blade.phpplugins/ecommerce::themes.includes.rating-star", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Frating-star.blade.php&line=1", "ajax": false, "filename": "rating-star.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.price", "param_count": null, "params": [], "start": 1764436476.029904, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/price.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fprice.blade.php&line=1", "ajax": false, "filename": "price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-price", "param_count": null, "params": [], "start": 1764436476.030752, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-price.blade.phpplugins/ecommerce::themes.includes.product-price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-price.blade.php&line=1", "ajax": false, "filename": "product-price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-prices.original", "param_count": null, "params": [], "start": 1764436476.037092, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-prices/original.blade.phpplugins/ecommerce::themes.includes.product-prices.original", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-prices%2Foriginal.blade.php&line=1", "ajax": false, "filename": "original.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.countdown", "param_count": null, "params": [], "start": 1764436476.039182, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/countdown.blade.phptheme.shofy::views.ecommerce.includes.product.countdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fcountdown.blade.php&line=1", "ajax": false, "filename": "countdown.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product-item", "param_count": null, "params": [], "start": 1764436476.040338, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product-item.blade.phptheme.shofy::views.ecommerce.includes.product-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct-item.blade.php&line=1", "ajax": false, "filename": "product-item.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.grid", "param_count": null, "params": [], "start": 1764436476.041608, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/grid.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.badges", "param_count": null, "params": [], "start": 1764436476.045344, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/badges.blade.phptheme.shofy::views.ecommerce.includes.product.badges", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fbadges.blade.php&line=1", "ajax": false, "filename": "badges.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "param_count": null, "params": [], "start": 1764436476.050341, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.rating", "param_count": null, "params": [], "start": 1764436476.072621, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/rating.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.rating", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Frating.blade.php&line=1", "ajax": false, "filename": "rating.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.rating-star", "param_count": null, "params": [], "start": 1764436476.074818, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/rating-star.blade.phpplugins/ecommerce::themes.includes.rating-star", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Frating-star.blade.php&line=1", "ajax": false, "filename": "rating-star.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.price", "param_count": null, "params": [], "start": 1764436476.077729, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/price.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fprice.blade.php&line=1", "ajax": false, "filename": "price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-price", "param_count": null, "params": [], "start": 1764436476.078762, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-price.blade.phpplugins/ecommerce::themes.includes.product-price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-price.blade.php&line=1", "ajax": false, "filename": "product-price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-prices.original", "param_count": null, "params": [], "start": 1764436476.086109, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-prices/original.blade.phpplugins/ecommerce::themes.includes.product-prices.original", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-prices%2Foriginal.blade.php&line=1", "ajax": false, "filename": "original.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.countdown", "param_count": null, "params": [], "start": 1764436476.087948, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/countdown.blade.phptheme.shofy::views.ecommerce.includes.product.countdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fcountdown.blade.php&line=1", "ajax": false, "filename": "countdown.blade.php", "line": "?"}}]}, "route": {"uri": "POST ajax/render-ui-blocks", "middleware": "Botble\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware, web, core, localeSessionRedirect, localizationRedirect", "controller": "Botble\\Shortcode\\Http\\Controllers\\ShortcodeController@ajaxRenderUiBlock", "namespace": null, "prefix": "/", "where": [], "as": "public.ajax.render-ui-block", "file": "<a href=\"phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fshortcode%2Fsrc%2FHttp%2FControllers%2FShortcodeController.php&line=57\" onclick=\"\">platform/packages/shortcode/src/Http/Controllers/ShortcodeController.php:57-74</a>"}, "queries": {"nb_statements": 27, "nb_visible_statements": 27, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02561, "accumulated_duration_str": "25.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-11-29' and `id` = '1' and `status` = 'published' limit 1", "type": "query", "params": [], "bindings": ["2025-11-29", "1", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 22, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 23, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 24, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}], "start": 1764436475.805517, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 0, "width_percent": 3.905}, {"sql": "select `ec_products`.*, (select avg(`ec_reviews`.`star`) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_avg`, (select count(*) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_count`, `ec_flash_sale_products`.`flash_sale_id` as `pivot_flash_sale_id`, `ec_flash_sale_products`.`product_id` as `pivot_product_id`, `ec_flash_sale_products`.`price` as `pivot_price`, `ec_flash_sale_products`.`quantity` as `pivot_quantity`, `ec_flash_sale_products`.`sold` as `pivot_sold` from `ec_products` inner join `ec_flash_sale_products` on `ec_products`.`id` = `ec_flash_sale_products`.`product_id` where `ec_flash_sale_products`.`flash_sale_id` in (1) and `status` = 'published' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 4", "type": "query", "params": [], "bindings": ["published", "published", "published", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 26, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 28, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}], "start": 1764436475.8157241, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 3.905, "width_percent": 10.699}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1, 2, 3, 4) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 32, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 33, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}], "start": 1764436475.8225532, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 14.604, "width_percent": 3.788}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 32, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 33, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}], "start": 1764436475.831337, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 18.391, "width_percent": 3.749}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 30, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 32, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}], "start": 1764436475.836445, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 23, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 22.14, "width_percent": 4.1}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 30, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 32, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}], "start": 1764436475.840739, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 23, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 26.24, "width_percent": 4.998}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` in (6, 7, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 32, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 33, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}], "start": 1764436475.844962, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 31.238, "width_percent": 2.772}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (6, 7, 8) and `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store'", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 31, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 38, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}], "start": 1764436475.848079, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 34.01, "width_percent": 2.929}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-11-29' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-11-29", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 25}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": 1764436475.880337, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 36.939, "width_percent": 3.163}, {"sql": "select `ec_products`.*, `ec_flash_sale_products`.`flash_sale_id` as `pivot_flash_sale_id`, `ec_flash_sale_products`.`product_id` as `pivot_product_id`, `ec_flash_sale_products`.`price` as `pivot_price`, `ec_flash_sale_products`.`quantity` as `pivot_quantity`, `ec_flash_sale_products`.`sold` as `pivot_sold` from `ec_products` inner join `ec_flash_sale_products` on `ec_products`.`id` = `ec_flash_sale_products`.`product_id` where `ec_flash_sale_products`.`flash_sale_id` in (1) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 25}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": 1764436475.8840408, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 40.102, "width_percent": 4.256}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-11-29 17:14:35' and (`end_date` is null or `end_date` >= '2025-11-29 17:14:35') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-11-29 17:14:35", "2025-11-29 17:14:35", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 116}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": 1764436475.8948739, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 44.358, "width_percent": 4.373}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436475.90518, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 48.731, "width_percent": 4.998}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 7 and not `parent_id` = 9 limit 1", "type": "query", "params": [], "bindings": [7, 9], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436475.909672, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 53.729, "width_percent": 3.124}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 2 and not `parent_id` = 7 limit 1", "type": "query", "params": [], "bindings": [2, 7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 122}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436475.912843, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 56.853, "width_percent": 2.109}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 2 limit 1", "type": "query", "params": [], "bindings": [0, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 122}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436475.915832, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 58.961, "width_percent": 2.772}, {"sql": "select * from `ec_brands` where `ec_brands`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1450}, {"index": 25, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436475.91912, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 61.734, "width_percent": 2.577}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": "view", "name": "plugins/ecommerce::themes.includes.product-price", "file": "D:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-price.blade.php", "line": 13}], "start": 1764436475.940165, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 64.311, "width_percent": 3.124}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 88}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436475.9629788, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 67.435, "width_percent": 4.412}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 32 limit 1", "type": "query", "params": [], "bindings": [0, 32], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436475.967548, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 71.847, "width_percent": 2.03}, {"sql": "select * from `ec_brands` where `ec_brands`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1450}, {"index": 25, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 88}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436475.970308, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 73.877, "width_percent": 3.397}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 3", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436476.005455, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 77.275, "width_percent": 4.295}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 32 and not `parent_id` = 36 limit 1", "type": "query", "params": [], "bindings": [32, 36], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436476.009921, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 81.57, "width_percent": 2.733}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 32 limit 1", "type": "query", "params": [], "bindings": [0, 32], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 122}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436476.013489, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 84.303, "width_percent": 2.811}, {"sql": "select * from `ec_brands` where `ec_brands`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1450}, {"index": 25, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436476.016211, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 87.114, "width_percent": 1.952}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436476.054268, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 89.067, "width_percent": 4.92}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 32 limit 1", "type": "query", "params": [], "bindings": [0, 32], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436476.0585861, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 93.987, "width_percent": 3.163}, {"sql": "select * from `ec_brands` where `ec_brands`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1450}, {"index": 25, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436476.062016, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 97.15, "width_percent": 2.85}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\ProductCategory": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCategory.php&line=1", "ajax": false, "filename": "ProductCategory.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Product": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductCollection": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCollection.php&line=1", "ajax": false, "filename": "ProductCollection.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Brand": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductVariation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1", "ajax": false, "filename": "ProductVariation.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\FlashSale": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FFlashSale.php&line=1", "ajax": false, "filename": "FlashSale.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductLabel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductLabel.php&line=1", "ajax": false, "filename": "ProductLabel.php", "line": "?"}}}, "count": 61, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "language": "en"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/a0797cf0-4da5-4463-91a4-d872e790981a\" target=\"_blank\">View in Telescope</a>", "path_info": "/ajax/render-ui-blocks", "status_code": "<pre class=sf-dump id=sf-dump-2063045748 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2063045748\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-311843434 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-311843434\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1971746341 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">ecommerce-flash-sale</span>\"\n  \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>style</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Deal of The Day</span>\"\n    \"<span class=sf-dump-key>flash_sale_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>4</span>\"\n    \"<span class=sf-dump-key>button_label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">View All Deals</span>\"\n    \"<span class=sf-dump-key>button_url</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/products</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971746341\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2103049754 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">226</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;142&quot;, &quot;Google Chrome&quot;;v=&quot;142&quot;, &quot;Not_A Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/142.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1957 characters\">botble_footprints_cookie=eyJpdiI6ImRFeFg2U3EwYUtoR05hRi9RVWdmN0E9PSIsInZhbHVlIjoiVk55ay9yby9GSzFqbEIzV2tzLzJtQis3OHJPWURuN014SmtndlQ4ZmhwN0JSNWdQMmxXVG9abmdrVGIwcENEdDVxc2tvWWlMK0s1WlBXa29wK1IzZUZWeHV6aGE0MTB3UU1YYy9OK0JYcXgvdUw3WGxVVGVWbVVkcStwbFBIWWEiLCJtYWMiOiJlMmI4MjIyMWM3NGI5OWUxYTU3M2RiMDlkNmIxZDE5ODE3NmIxNDJhNzJjODFiYjllYTk2NjY4NDUzMWZhNGUwIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IkZ6Z1d1ZjZtRlhZdy80MFJxOER2T0E9PSIsInZhbHVlIjoibDhQbEMyOStBTlQ5czkzYWRIMGczRDhRMEFVMWNYOVQ1eU0zZ1hYVlp2VWhGUVZmYlYxTksrS0I4aWI3b0FMTXRDd2sybTRGSFZwRGEzN0JTQzZMWFF0S0wwMndURVhIRm51UFQvVElkSFR6dmlBWjRKMVROamc4dW9HTGJselFDTlZQNC9qeDdNOXJvd0djRmN1anc3UHdSL2p1cE9RZGxROEc0SE15elhQUFJ4Q2Y3ODhycmVoM1EwNUJaQTRCY0VXVzNiUmNnS21GM1hWMnJwYUNXWHlIUFd6cmlYRlBnNnMwY3lpK0MzR21lUjRtSm1NZHRPbmUvR2szQngwdW4wZUhVM1Z4NC9PVGxJdG9pVWdPK2RZTXF4N1lUWjR3ZndTeGVFKzZVTmE0RHI4cWp2akJJaEo4WmZWc0s1Y0kwdWx1TGNEeW45ZEJzbDZNeWtadXJKMi9OOHJTcUZrVEIrRTBRa1RNZjFNUVJyaHpWYzdObHpjNU5zMUtvVlVQUlN6amt1aEcwR25iTWhSdFpYcG1jZGFmS0QyN01JVTdpcmtBeDVSQ1BpaVFxbjhBMVZtNEo2eGxGL2lpcjJiT1JreWdFQ3FkcFZraDg3MUNqU253ZmJGck9aSGc0NzNOVWpOUjBoNzU1VFE9IiwibWFjIjoiMTE0ZjdmM2EzODVjMWUxYzFmYzgwNmZhNWEwYzZjYTQwOTc2ODVkNTk1NDZkZGE0MzAwODc5OGMyNzg4ODc2ZCIsInRhZyI6IiJ9; newsletter_popup=1; XSRF-TOKEN=eyJpdiI6InlVaU1EWmY3QnIwZERtMnVzTmIwYWc9PSIsInZhbHVlIjoieHZ2NGdRVzVLQTREQnJhV3d1NXQ0OHhSRWsrSml3SlJwTkh2bmM5QVkxMkVyNHgrSE9Wd3pva010UGV2VHV6N3BWU2hZOFVhWC9ubFFYdGVDUVlYL3Jac0VwdEtpTm9yUWZCYUxzQ1laRFBOZStwSFFhRmhaZmlxUjRJQ2U4NWIiLCJtYWMiOiJmMTgwMTYyNjRlYzFiZmJmNGUwNWE5OTk2NDRkYzU5OGFjMDY0YTRiMmUzNmY3ZWYyM2U1ZDFjNzNjNzNhOTRhIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6ImpqQTk4RlhmSUtMWUwrR1BDQTFpMWc9PSIsInZhbHVlIjoiV2hJZmNvRVFScU02VWpaUDhXTkQySGtrMVBSemZSVmtyYitxVXFzYmVLUkdSTjJZUEFjcjQrdng0WDVSQmFuNi9OZzVZQU1DMWJGMW5QakhFeFNlRENIZXhzcmZkejNaZTNkbFpIVk4yZ1pVaGJKeHcvWGxiTDZSRnVtSFZuU3ciLCJtYWMiOiJkYWNhZjk4YTk1YTQyOWJkNTg5YTJiZTc0Y2YxMWU1ZjM0ZGJjYWU1YWIwNjRiNThjOWE4MzMwMDBhZWYzNmQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103049754\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1433076542 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5832e034bf28c6f97be3a608bb8c655f40381a49</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"320 characters\">{&quot;footprint&quot;:&quot;5832e034bf28c6f97be3a608bb8c655f40381a49&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;127.0.0.1&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>newsletter_popup</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PMPD3sRy7YrBqKNvMw96LkySo4AQ8DJuqi1a8JCS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1433076542\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2050215174 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 29 Nov 2025 17:14:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlkwQm1NakEvZzRYZUVKZER4VEJYUkE9PSIsInZhbHVlIjoiMTRIYUhjNzVWVTRaUW8rc0xqWVpmMUgvQVRvZWJja1lqZWNpM2xCTzB1aTM1NWVEWUhpQTZTejE2bmR3MVA5RzZJckFvdHpqSGlweXd6dGo5dGE0NW9EQ2dIdjh6b3htdXJickZPTUp6OUJmSVBjaEw3ODRxVkJsWStieWt3L3MiLCJtYWMiOiI1OTFiM2U4OWNmYWNmYTM1YmIzNWMwMzFjYTZiNWFjMmI5YmI0NzRkMDdlMTk2NjUyMzY5MjY4M2Y3MGQzZTVhIiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:14:36 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">botble_session=eyJpdiI6Ik1kS3kwUWRweWxOZE9nVGZzSUlqUWc9PSIsInZhbHVlIjoiZFdRZFNJSXl0RGtlTmZjdUxqa01OL0hMOUl4TkZIT1ZMRXhPMWV4ZDRYWGdQUXhwWE5yS0dZeFlzR1IvY3FwVnhMRzc0UzNVVTdJb1RnWEVXNWdmQkkxWnFtTkFBRzNZMUdkN2thK2JucGYyWCtJKzIwZEdlWHU3UDBVVEFqNmwiLCJtYWMiOiJkMDFiYmZlYmZiMDAzMTBjZDkzNTY5Y2E1YzY4YWFlMDRlN2FjMTAyYTAzODA3MWQwYmUxZDRiN2NiNTQzODNlIiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:14:36 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlkwQm1NakEvZzRYZUVKZER4VEJYUkE9PSIsInZhbHVlIjoiMTRIYUhjNzVWVTRaUW8rc0xqWVpmMUgvQVRvZWJja1lqZWNpM2xCTzB1aTM1NWVEWUhpQTZTejE2bmR3MVA5RzZJckFvdHpqSGlweXd6dGo5dGE0NW9EQ2dIdjh6b3htdXJickZPTUp6OUJmSVBjaEw3ODRxVkJsWStieWt3L3MiLCJtYWMiOiI1OTFiM2U4OWNmYWNmYTM1YmIzNWMwMzFjYTZiNWFjMmI5YmI0NzRkMDdlMTk2NjUyMzY5MjY4M2Y3MGQzZTVhIiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:14:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">botble_session=eyJpdiI6Ik1kS3kwUWRweWxOZE9nVGZzSUlqUWc9PSIsInZhbHVlIjoiZFdRZFNJSXl0RGtlTmZjdUxqa01OL0hMOUl4TkZIT1ZMRXhPMWV4ZDRYWGdQUXhwWE5yS0dZeFlzR1IvY3FwVnhMRzc0UzNVVTdJb1RnWEVXNWdmQkkxWnFtTkFBRzNZMUdkN2thK2JucGYyWCtJKzIwZEdlWHU3UDBVVEFqNmwiLCJtYWMiOiJkMDFiYmZlYmZiMDAzMTBjZDkzNTY5Y2E1YzY4YWFlMDRlN2FjMTAyYTAzODA3MWQwYmUxZDRiN2NiNTQzODNlIiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:14:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050215174\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-181656582 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-181656582\", {\"maxDepth\":0})</script>\n"}}