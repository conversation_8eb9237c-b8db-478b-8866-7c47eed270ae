<?php

namespace Bo<PERSON>ble\Shortcode\Http\Controllers;

use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Base\Forms\FormAbstract;
use <PERSON><PERSON>ble\Base\Http\Controllers\BaseController;
use <PERSON><PERSON>ble\Shortcode\Events\ShortcodeAdminConfigRendering;
use <PERSON><PERSON>ble\Shortcode\Facades\Shortcode;
use Bo<PERSON>ble\Shortcode\Http\Requests\GetShortcodeDataRequest;
use Bo<PERSON>ble\Shortcode\Http\Requests\RenderBlockUiRequest;
use Botble\Support\Services\Cache\Cache;
use Carbon\Carbon;
use Closure;
use Illuminate\Support\Arr;

class ShortcodeController extends BaseController
{
    public function ajaxGetAdminConfig(?string $key, GetShortcodeDataRequest $request)
    {
        ShortcodeAdminConfigRendering::dispatch();

        $registered = shortcode()->getAll();

        $key = $key ?: $request->input('key');

        $data = Arr::get($registered, $key . '.admin_config');

        $attributes = [];
        $content = null;

        if ($code = $request->input('code')) {
            $compiler = shortcode()->getCompiler();
            $attributes = $compiler->getAttributes(html_entity_decode($code));
            $content = $compiler->getContent();
        }

        if ($data instanceof Closure || is_callable($data)) {
            $data = call_user_func($data, $attributes, $content);

            if ($modifier = Arr::get($registered, $key . '.admin_config_modifier')) {
                $data = call_user_func($modifier, $data, $attributes, $content);
            }

            $data = $data instanceof FormAbstract ? $data->renderForm() : $data;
        }

        $data = apply_filters(SHORTCODE_REGISTER_CONTENT_IN_ADMIN, $data, $key, $attributes);

        if (! $data) {
            $data = Html::tag('code', Shortcode::generateShortcode($key, $attributes))->toHtml();
        }

        return $this
            ->httpResponse()
            ->setData($data);
    }

    public function ajaxRenderUiBlock(RenderBlockUiRequest $request)
    {
        $name = $request->input('name');
        $attributes = $request->input('attributes', []);

        // Validate shortcode exists
        if (! in_array($name, array_keys(Shortcode::getAll()))) {
            return $this
                ->httpResponse()
                ->setData(null);
        }

        // Create cache key based on shortcode name and attributes
        $cache = Cache::make('shortcode');
        $cacheKey = 'render_ui_block_' . md5(serialize([
            'name' => $name,
            'attributes' => $attributes,
            'locale' => app()->getLocale(),
            'theme' => app('theme')->getThemeName(),
        ]));

        // Cache the rendered content
        // Different cache times based on shortcode type
        $cacheTime = $this->getCacheTimeForShortcode($name);

        return $cache->remember($cacheKey, $cacheTime, function () use ($name, $attributes) {
            $code = Shortcode::generateShortcode($name, $attributes);
            $content = Shortcode::compile($code, true)->toHtml();

            return $this
                ->httpResponse()
                ->setData($content);
        });
    }

    /**
     * Get cache time based on shortcode type
     */
    protected function getCacheTimeForShortcode(string $name): Carbon
    {
        // Dynamic content shortcodes get shorter cache time
        $dynamicShortcodes = [
            'blog-posts',
            'ecommerce-products',
            'ecommerce-flash-sale',
            'ecommerce-product-categories',
            'ecommerce-brands',
        ];

        // Static content shortcodes get longer cache time
        $staticShortcodes = [
            'site-features',
            'about',
            'image-slider',
            'google-map',
            'youtube-video',
            'custom-html',
        ];

        if (in_array($name, $dynamicShortcodes)) {
            // Dynamic content: 30 minutes cache
            return Carbon::now()->addMinutes(30);
        } elseif (in_array($name, $staticShortcodes)) {
            // Static content: 4 hours cache
            return Carbon::now()->addHours(4);
        }

        // Default: 1 hour cache
        return Carbon::now()->addHour();
    }
}
