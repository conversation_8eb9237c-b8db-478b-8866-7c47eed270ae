{"__meta": {"id": "Xede608630c411fe82c8033276246c0c7", "datetime": "2025-11-29 17:08:28", "utime": 1764436108.70572, "method": "POST", "uri": "/ajax/render-ui-blocks", "ip": "127.0.0.1"}, "php": {"version": "8.2.29", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1764436108.164949, "end": 1764436108.705745, "duration": 0.5407960414886475, "duration_str": "541ms", "measures": [{"label": "Booting", "start": 1764436108.164949, "relative_start": 0, "end": 1764436108.634876, "relative_end": 1764436108.634876, "duration": 0.4699270725250244, "duration_str": "470ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1764436108.634887, "relative_start": 0.4699380397796631, "end": 1764436108.705748, "relative_end": 3.0994415283203125e-06, "duration": 0.0708611011505127, "duration_str": "70.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51505184, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 5, "templates": [{"name": "theme.shofy::partials.shortcodes.ads.index", "param_count": null, "params": [], "start": 1764436108.674507, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ads/index.blade.phptheme.shofy::partials.shortcodes.ads.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fads%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.shortcodes.ads.style-4", "param_count": null, "params": [], "start": 1764436108.675056, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ads/style-4.blade.phptheme.shofy::partials.shortcodes.ads.style-4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fads%2Fstyle-4.blade.php&line=1", "ajax": false, "filename": "style-4.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.shortcodes.ads.includes.item", "param_count": null, "params": [], "start": 1764436108.682323, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ads/includes/item.blade.phptheme.shofy::partials.shortcodes.ads.includes.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fads%2Fincludes%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.shortcodes.ads.includes.item", "param_count": null, "params": [], "start": 1764436108.695338, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ads/includes/item.blade.phptheme.shofy::partials.shortcodes.ads.includes.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fads%2Fincludes%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.shortcodes.ads.includes.item", "param_count": null, "params": [], "start": 1764436108.700976, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ads/includes/item.blade.phptheme.shofy::partials.shortcodes.ads.includes.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fads%2Fincludes%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}]}, "route": {"uri": "POST ajax/render-ui-blocks", "middleware": "Botble\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware, web, core, localeSessionRedirect, localizationRedirect", "controller": "Botble\\Shortcode\\Http\\Controllers\\ShortcodeController@ajaxRenderUiBlock", "namespace": null, "prefix": "/", "where": [], "as": "public.ajax.render-ui-block", "file": "<a href=\"phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fshortcode%2Fsrc%2FHttp%2FControllers%2FShortcodeController.php&line=57\" onclick=\"\">platform/packages/shortcode/src/Http/Controllers/ShortcodeController.php:57-74</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0028200000000000005, "accumulated_duration_str": "2.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `ads` where `key` in ('B5ZA76ZWMWAE', 'F1LTQS976YPY', 'IHPZ2WBSYJUK', null) and `status` = 'published' order by `order` asc", "type": "query", "params": [], "bindings": ["B5ZA76ZWMWAE", "F1LTQS976YPY", "IHPZ2WBSYJUK", null, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ads.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ads.php", "line": 32}, {"index": 21, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 22, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 23, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}], "start": 1764436108.6703699, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 0, "width_percent": 32.979}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Botble\\\\Ads\\\\Models\\\\Ads' and `meta_boxes`.`reference_id` = 5 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Botble\\Ads\\Models\\Ads", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.shofy::partials.shortcodes.ads.style-4", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ads/style-4.blade.php", "line": 8}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436108.6773221, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 32.979, "width_percent": 22.695}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Botble\\\\Ads\\\\Models\\\\Ads' and `meta_boxes`.`reference_id` = 6 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Botble\\Ads\\Models\\Ads", 6], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.shofy::partials.shortcodes.ads.style-4", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ads/style-4.blade.php", "line": 8}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436108.691011, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 55.674, "width_percent": 24.468}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Botble\\\\Ads\\\\Models\\\\Ads' and `meta_boxes`.`reference_id` = 7 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Botble\\Ads\\Models\\Ads", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": "view", "name": "theme.shofy::partials.shortcodes.ads.style-4", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ads/style-4.blade.php", "line": 8}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436108.69701, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 80.142, "width_percent": 19.858}]}, "models": {"data": {"Botble\\Base\\Models\\MetaBox": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}, "Botble\\Ads\\Models\\Ads": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fads%2Fsrc%2FModels%2FAds.php&line=1", "ajax": false, "filename": "Ads.php", "line": "?"}}}, "count": 12, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "language": "en"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/a0797abf-b46a-4d94-aec6-c6cdb03aff86\" target=\"_blank\">View in Telescope</a>", "path_info": "/ajax/render-ui-blocks", "status_code": "<pre class=sf-dump id=sf-dump-79195845 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-79195845\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1617325619 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1617325619\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-956428331 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ads</span>\"\n  \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>style</span>\" => \"<span class=sf-dump-str>4</span>\"\n    \"<span class=sf-dump-key>key_1</span>\" => \"<span class=sf-dump-str title=\"12 characters\">B5ZA76ZWMWAE</span>\"\n    \"<span class=sf-dump-key>key_2</span>\" => \"<span class=sf-dump-str title=\"12 characters\">F1LTQS976YPY</span>\"\n    \"<span class=sf-dump-key>key_3</span>\" => \"<span class=sf-dump-str title=\"12 characters\">IHPZ2WBSYJUK</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956428331\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-605162203 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">137</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;142&quot;, &quot;Google Chrome&quot;;v=&quot;142&quot;, &quot;Not_A Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/142.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1957 characters\">botble_footprints_cookie=eyJpdiI6ImRFeFg2U3EwYUtoR05hRi9RVWdmN0E9PSIsInZhbHVlIjoiVk55ay9yby9GSzFqbEIzV2tzLzJtQis3OHJPWURuN014SmtndlQ4ZmhwN0JSNWdQMmxXVG9abmdrVGIwcENEdDVxc2tvWWlMK0s1WlBXa29wK1IzZUZWeHV6aGE0MTB3UU1YYy9OK0JYcXgvdUw3WGxVVGVWbVVkcStwbFBIWWEiLCJtYWMiOiJlMmI4MjIyMWM3NGI5OWUxYTU3M2RiMDlkNmIxZDE5ODE3NmIxNDJhNzJjODFiYjllYTk2NjY4NDUzMWZhNGUwIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IkZ6Z1d1ZjZtRlhZdy80MFJxOER2T0E9PSIsInZhbHVlIjoibDhQbEMyOStBTlQ5czkzYWRIMGczRDhRMEFVMWNYOVQ1eU0zZ1hYVlp2VWhGUVZmYlYxTksrS0I4aWI3b0FMTXRDd2sybTRGSFZwRGEzN0JTQzZMWFF0S0wwMndURVhIRm51UFQvVElkSFR6dmlBWjRKMVROamc4dW9HTGJselFDTlZQNC9qeDdNOXJvd0djRmN1anc3UHdSL2p1cE9RZGxROEc0SE15elhQUFJ4Q2Y3ODhycmVoM1EwNUJaQTRCY0VXVzNiUmNnS21GM1hWMnJwYUNXWHlIUFd6cmlYRlBnNnMwY3lpK0MzR21lUjRtSm1NZHRPbmUvR2szQngwdW4wZUhVM1Z4NC9PVGxJdG9pVWdPK2RZTXF4N1lUWjR3ZndTeGVFKzZVTmE0RHI4cWp2akJJaEo4WmZWc0s1Y0kwdWx1TGNEeW45ZEJzbDZNeWtadXJKMi9OOHJTcUZrVEIrRTBRa1RNZjFNUVJyaHpWYzdObHpjNU5zMUtvVlVQUlN6amt1aEcwR25iTWhSdFpYcG1jZGFmS0QyN01JVTdpcmtBeDVSQ1BpaVFxbjhBMVZtNEo2eGxGL2lpcjJiT1JreWdFQ3FkcFZraDg3MUNqU253ZmJGck9aSGc0NzNOVWpOUjBoNzU1VFE9IiwibWFjIjoiMTE0ZjdmM2EzODVjMWUxYzFmYzgwNmZhNWEwYzZjYTQwOTc2ODVkNTk1NDZkZGE0MzAwODc5OGMyNzg4ODc2ZCIsInRhZyI6IiJ9; newsletter_popup=1; XSRF-TOKEN=eyJpdiI6InZyWjdseXJNQTV0TGFNRlpmY0lZT1E9PSIsInZhbHVlIjoibWRldkJVWjFOM01pMzQyZnRaSGVvZHJXMDBqalBYVHcxc2lMQ0hEME1RRkJ2ajZMTWZOZU9FaE4wWDhGbTVydXgwVnJDWHlFdnNOZVJWc0NzTHgyamMxb3ByZnA5ZEI4dEl6OHVFOG5ROFRpdkEwZDViMFZ2Y3V5QU9XemY4TlUiLCJtYWMiOiI5ZTRmMWYyZGZkN2M0YmNlZTdmYjVhYzE0ODNjZjcyYTY2Njg2OWZjY2U5MTc0ZGQ0MGY0ZTY2YjJmOWFhMzc5IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IkpaSjIrOGZwVVZ0RGNKTit3U1N2cnc9PSIsInZhbHVlIjoiTCtmVFZabmk2RTdBaDNYRWFKYzIyWEE3OWNOcnM1YzFQNzE0VDY0aysxQWhzdCswaTdmN3p5NTN5SWEza3ZjZEJwVTBMUmxHckpTOS9uS0dwR044cmlIUUE1UVdMeDZudWJDU0tGY29FZlgxb1ljekpsa2pLT3BVYTJiall3WGEiLCJtYWMiOiJmMjUzZTYyMjY1YWRmOTA1YTFiMTBiZjE1NmYyNDNhY2NiNzM3YjMyNTc0MTU1NmM3YzJhYzUxZDFjZTI0NzU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-605162203\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-13640654 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5832e034bf28c6f97be3a608bb8c655f40381a49</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"320 characters\">{&quot;footprint&quot;:&quot;5832e034bf28c6f97be3a608bb8c655f40381a49&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;127.0.0.1&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>newsletter_popup</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PMPD3sRy7YrBqKNvMw96LkySo4AQ8DJuqi1a8JCS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13640654\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-626249438 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 29 Nov 2025 17:08:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlNrUk4zMU9tRXNNcEdZQ21sUktrTXc9PSIsInZhbHVlIjoic2NjWVVoMW1SZHhoOHRwOGNlVEZFS05nUW85by9lalZ0T3FpQUpYRC9yUXFlVTl3MDZQTGZncytjL1BXZjZvNk5UQ3VxZWVqcXZJYTJKMU02eDY1dkpmK3ZQbWkySWkwRVQxemFWamJ0WkNGMXpnemFZMnBqWHJBbXhCaWUxSlkiLCJtYWMiOiI2OGYzZWQzYjdlY2E5YzI5YTVhYzVlY2RiMzFiZWFiMzk4ZDM0ZmRjNTdjNGFjZmUwNzQ5MmU0N2NiYzc5YmM5IiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:08:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">botble_session=eyJpdiI6ImxOdC9FZ0tXQ3hSSktyQjBDMlZjREE9PSIsInZhbHVlIjoiUXVJS1lhQWVPNllrVGFvZkVaS0UwWE0weFZRL0hYdWJaSnBSNXROVzBBVnBzaDRjRWwvZnlIdC9YOUtqR3ZMMVhPQldIVnNMbmNRUUY5ajlJcWtabnJvc2YxMTRPNVhUR21aNU5rN1B6bCtQc2tUVXdWZHlqK2VtQnpOYnBnMlciLCJtYWMiOiJiNTczY2UyZGE0MGI4YWQ2NjI0MGRlYzNiZWU4OTg5N2ZjYzY0NTlkY2I2YTgzOTA3NjEwNjg0ODAzNjZjMWEyIiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:08:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlNrUk4zMU9tRXNNcEdZQ21sUktrTXc9PSIsInZhbHVlIjoic2NjWVVoMW1SZHhoOHRwOGNlVEZFS05nUW85by9lalZ0T3FpQUpYRC9yUXFlVTl3MDZQTGZncytjL1BXZjZvNk5UQ3VxZWVqcXZJYTJKMU02eDY1dkpmK3ZQbWkySWkwRVQxemFWamJ0WkNGMXpnemFZMnBqWHJBbXhCaWUxSlkiLCJtYWMiOiI2OGYzZWQzYjdlY2E5YzI5YTVhYzVlY2RiMzFiZWFiMzk4ZDM0ZmRjNTdjNGFjZmUwNzQ5MmU0N2NiYzc5YmM5IiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:08:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">botble_session=eyJpdiI6ImxOdC9FZ0tXQ3hSSktyQjBDMlZjREE9PSIsInZhbHVlIjoiUXVJS1lhQWVPNllrVGFvZkVaS0UwWE0weFZRL0hYdWJaSnBSNXROVzBBVnBzaDRjRWwvZnlIdC9YOUtqR3ZMMVhPQldIVnNMbmNRUUY5ajlJcWtabnJvc2YxMTRPNVhUR21aNU5rN1B6bCtQc2tUVXdWZHlqK2VtQnpOYnBnMlciLCJtYWMiOiJiNTczY2UyZGE0MGI4YWQ2NjI0MGRlYzNiZWU4OTg5N2ZjYzY0NTlkY2I2YTgzOTA3NjEwNjg0ODAzNjZjMWEyIiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:08:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-626249438\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1050590191 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1050590191\", {\"maxDepth\":0})</script>\n"}}