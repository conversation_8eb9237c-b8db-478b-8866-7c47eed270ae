# AJAX Endpoints Documentation

## Overview

This document describes the AJAX endpoints available in the Shofy theme for dynamic content loading with caching implementation.

## 1. AJAX Products Endpoint

### Route Information
- **URL**: `/ajax/products`
- **Method**: `GET`
- **Route Name**: `public.ajax.products`
- **Controller**: `<PERSON>hofyController@ajaxGetProducts`

### Parameters

#### Limit
- **Parameter**: `limit`
- **Type**: `integer`
- **Default**: `10`
- **Description**: Maximum number of products to return
- **Example**: `/ajax/products?limit=8`

#### Product Type
- **Parameter**: `type`
- **Type**: `string`
- **Default**: `all`
- **Options**: `all`, `featured`, `on-sale`, `trending`, `top-rated`
- **Description**: Filter products by specific type
- **Example**: `/ajax/products?type=featured&limit=8`

#### Search Query
- **Parameter**: `q`
- **Type**: `string`
- **Description**: Search term to filter products
- **Example**: `/ajax/products?q=iPhone&limit=5`

### Response Format

```json
{
    "error": false,
    "data": {
        "count": "8",
        "html": "<div class=\"product-grid\">...</div>"
    },
    "message": "8 Products found"
}
```

### Caching

- **Search requests**: Cached for 15 minutes
- **Non-search requests**: Cached for 2 hours
- **Cache key**: Based on limit, type, query, layout, and items per row

### Usage Examples

```javascript
// Get 8 products of all types
fetch('/ajax/products?limit=8&type=all')
    .then(response => response.json())
    .then(data => {
        document.getElementById('products-container').innerHTML = data.data.html;
    });

// Get featured products
fetch('/ajax/products?limit=5&type=featured')
    .then(response => response.json())
    .then(data => {
        document.getElementById('featured-products').innerHTML = data.data.html;
    });

// Search products
fetch('/ajax/products?q=smartphone&limit=10')
    .then(response => response.json())
    .then(data => {
        document.getElementById('search-results').innerHTML = data.data.html;
    });
```

## 2. AJAX Categories Dropdown Endpoint

### Route Information
- **URL**: `/ajax/categories-dropdown`
- **Method**: `GET`
- **Route Name**: `public.ajax.categories-dropdown`
- **Controller**: `PublicAjaxController@ajaxGetCategoriesDropdown`

### Parameters

This endpoint doesn't accept any parameters.

### Response Format

```json
{
    "error": false,
    "data": {
        "select": "<option value=\"\">All Categories</option><option value=\"1\">Electronics</option>...",
        "dropdown": "<ul><li><a href=\"...\">Electronics</a></li>...</ul>"
    }
}
```

### Response Fields

#### Select
- **Type**: `string`
- **Description**: HTML for a select dropdown with all published categories
- **Usage**: For search filters and forms

#### Dropdown
- **Type**: `string|null`
- **Description**: HTML for a navigation dropdown menu
- **Usage**: For header navigation menus
- **Note**: Returns `null` if the theme view doesn't exist

### Caching

- **Cache duration**: 2 hours
- **Cache key**: Based on view existence, theme name, and locale
- **Cache invalidation**: Automatic when categories are updated

### Usage Examples

```javascript
// Load categories dropdown
fetch('/ajax/categories-dropdown')
    .then(response => response.json())
    .then(data => {
        // Update select dropdown
        document.getElementById('category-select').innerHTML = data.data.select;
        
        // Update navigation dropdown (if available)
        if (data.data.dropdown) {
            document.getElementById('nav-categories').innerHTML = data.data.dropdown;
        }
    });
```

## Performance Considerations

### Caching Strategy
1. **Products**: Different cache times for search vs. browsing
2. **Categories**: Longer cache time due to less frequent changes
3. **Cache keys**: Include relevant parameters to avoid conflicts

### Optimization Tips
1. Use appropriate `limit` values to avoid large responses
2. Cache responses on the frontend for repeated requests
3. Consider using ETags for conditional requests
4. Monitor cache hit rates and adjust TTL as needed

## Error Handling

Both endpoints include proper error handling:
- Invalid parameters are filtered out
- Database errors are caught and handled gracefully
- Proper HTTP status codes are returned
- JSON error responses for AJAX requests

## Security

- All input is properly sanitized through Laravel's request handling
- SQL injection protection through Eloquent ORM
- XSS protection through proper HTML escaping in views
- CSRF protection for state-changing operations

## Testing

### Manual Testing
```bash
# Test products endpoint
curl "http://yourdomain.com/ajax/products?limit=8&type=all"

# Test categories dropdown
curl "http://yourdomain.com/ajax/categories-dropdown"
```

### Automated Testing
```bash
# Run product tests
php artisan test tests/Feature/AjaxProductSearchTest.php

# Run categories tests
php artisan test tests/Feature/AjaxCategoriesDropdownTest.php
```
