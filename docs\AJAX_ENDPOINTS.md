# AJAX Endpoints Documentation

## Overview

This document describes the AJAX endpoints available in the Shofy theme for dynamic content loading with caching implementation.

## 1. AJAX Products Endpoint

### Route Information
- **URL**: `/ajax/products`
- **Method**: `GET`
- **Route Name**: `public.ajax.products`
- **Controller**: `<PERSON>hofyController@ajaxGetProducts`

### Parameters

#### Limit
- **Parameter**: `limit`
- **Type**: `integer`
- **Default**: `10`
- **Description**: Maximum number of products to return
- **Example**: `/ajax/products?limit=8`

#### Product Type
- **Parameter**: `type`
- **Type**: `string`
- **Default**: `all`
- **Options**: `all`, `featured`, `on-sale`, `trending`, `top-rated`
- **Description**: Filter products by specific type
- **Example**: `/ajax/products?type=featured&limit=8`

#### Search Query
- **Parameter**: `q`
- **Type**: `string`
- **Description**: Search term to filter products
- **Example**: `/ajax/products?q=iPhone&limit=5`

### Response Format

```json
{
    "error": false,
    "data": {
        "count": "8",
        "html": "<div class=\"product-grid\">...</div>"
    },
    "message": "8 Products found"
}
```

### Caching

- **Search requests**: Cached for 15 minutes
- **Non-search requests**: Cached for 2 hours
- **Cache key**: Based on limit, type, query, layout, and items per row

### Usage Examples

```javascript
// Get 8 products of all types
fetch('/ajax/products?limit=8&type=all')
    .then(response => response.json())
    .then(data => {
        document.getElementById('products-container').innerHTML = data.data.html;
    });

// Get featured products
fetch('/ajax/products?limit=5&type=featured')
    .then(response => response.json())
    .then(data => {
        document.getElementById('featured-products').innerHTML = data.data.html;
    });

// Search products
fetch('/ajax/products?q=smartphone&limit=10')
    .then(response => response.json())
    .then(data => {
        document.getElementById('search-results').innerHTML = data.data.html;
    });
```

## 2. AJAX Categories Dropdown Endpoint

### Route Information
- **URL**: `/ajax/categories-dropdown`
- **Method**: `GET`
- **Route Name**: `public.ajax.categories-dropdown`
- **Controller**: `PublicAjaxController@ajaxGetCategoriesDropdown`

### Parameters

This endpoint doesn't accept any parameters.

### Response Format

```json
{
    "error": false,
    "data": {
        "select": "<option value=\"\">All Categories</option><option value=\"1\">Electronics</option>...",
        "dropdown": "<ul><li><a href=\"...\">Electronics</a></li>...</ul>"
    }
}
```

### Response Fields

#### Select
- **Type**: `string`
- **Description**: HTML for a select dropdown with all published categories
- **Usage**: For search filters and forms

#### Dropdown
- **Type**: `string|null`
- **Description**: HTML for a navigation dropdown menu
- **Usage**: For header navigation menus
- **Note**: Returns `null` if the theme view doesn't exist

### Caching

- **Cache duration**: 2 hours
- **Cache key**: Based on view existence, theme name, and locale
- **Cache invalidation**: Automatic when categories are updated

### Usage Examples

```javascript
// Load categories dropdown
fetch('/ajax/categories-dropdown')
    .then(response => response.json())
    .then(data => {
        // Update select dropdown
        document.getElementById('category-select').innerHTML = data.data.select;
        
        // Update navigation dropdown (if available)
        if (data.data.dropdown) {
            document.getElementById('nav-categories').innerHTML = data.data.dropdown;
        }
    });
```

## Performance Considerations

### Caching Strategy
1. **Products**: Different cache times for search vs. browsing
2. **Categories**: Longer cache time due to less frequent changes
3. **Cache keys**: Include relevant parameters to avoid conflicts

### Optimization Tips
1. Use appropriate `limit` values to avoid large responses
2. Cache responses on the frontend for repeated requests
3. Consider using ETags for conditional requests
4. Monitor cache hit rates and adjust TTL as needed

## Error Handling

Both endpoints include proper error handling:
- Invalid parameters are filtered out
- Database errors are caught and handled gracefully
- Proper HTTP status codes are returned
- JSON error responses for AJAX requests

## Security

- All input is properly sanitized through Laravel's request handling
- SQL injection protection through Eloquent ORM
- XSS protection through proper HTML escaping in views
- CSRF protection for state-changing operations

## 3. AJAX Render UI Blocks Endpoint

### Route Information
- **URL**: `/ajax/render-ui-blocks`
- **Method**: `POST`
- **Route Name**: `public.ajax.render-ui-block`
- **Controller**: `ShortcodeController@ajaxRenderUiBlock`

### Parameters

#### Name (Required)
- **Parameter**: `name`
- **Type**: `string`
- **Description**: The shortcode name to render
- **Example**: `site-features`, `blog-posts`, `ecommerce-products`

#### Attributes (Optional)
- **Parameter**: `attributes`
- **Type**: `array`
- **Description**: Shortcode attributes/configuration
- **Example**: `{"title": "Featured Products", "limit": "8"}`

### Request Format

```json
{
    "name": "site-features",
    "attributes": {
        "title": "Our Features",
        "subtitle": "What we offer"
    }
}
```

### Response Format

```json
{
    "error": false,
    "data": "<div class=\"site-features\">...</div>"
}
```

### Available Shortcodes

#### Static Content Shortcodes (4 hours cache)
- `site-features` - Display site features
- `about` - About section
- `image-slider` - Image carousel
- `google-map` - Google Maps embed
- `youtube-video` - YouTube video embed
- `custom-html` - Custom HTML content

#### Dynamic Content Shortcodes (30 minutes cache)
- `blog-posts` - Blog posts listing
- `ecommerce-products` - Product listings
- `ecommerce-flash-sale` - Flash sale products
- `ecommerce-product-categories` - Product categories
- `ecommerce-brands` - Brand listings

#### Default Cache (1 hour)
- All other registered shortcodes

### Caching

- **Static content**: Cached for 4 hours
- **Dynamic content**: Cached for 30 minutes
- **Default**: Cached for 1 hour
- **Cache key**: Based on shortcode name, attributes, locale, and theme

### Usage Examples

```javascript
// Render site features
fetch('/ajax/render-ui-blocks', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
        name: 'site-features',
        attributes: {
            title: 'Our Amazing Features'
        }
    })
})
.then(response => response.json())
.then(data => {
    document.getElementById('features-container').innerHTML = data.data;
});

// Render blog posts
fetch('/ajax/render-ui-blocks', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
        name: 'blog-posts',
        attributes: {
            type: 'featured',
            limit: '6'
        }
    })
})
.then(response => response.json())
.then(data => {
    document.getElementById('blog-posts').innerHTML = data.data;
});
```

## Testing

### Manual Testing
```bash
# Test products endpoint
curl "http://yourdomain.com/ajax/products?limit=8&type=all"

# Test categories dropdown
curl "http://yourdomain.com/ajax/categories-dropdown"

# Test render UI blocks
curl -X POST "http://yourdomain.com/ajax/render-ui-blocks" \
  -H "Content-Type: application/json" \
  -H "X-CSRF-TOKEN: your-csrf-token" \
  -d '{"name": "site-features", "attributes": {"title": "Test"}}'
```

### Automated Testing
```bash
# Run product tests
php artisan test tests/Feature/AjaxProductSearchTest.php

# Run categories tests
php artisan test tests/Feature/AjaxCategoriesDropdownTest.php

# Run UI blocks tests
php artisan test tests/Feature/AjaxRenderUiBlocksTest.php

# Run comprehensive test script
php test_ajax_endpoints.php
```
