1764521755s:9142:"<ul >
            <li class="has-dropdown">
            <a href="http://127.0.0.1:8000/home"
               title="Home"
                           >
                

                Home

                                    <svg class="icon  svg-icon-ti-ti-chevron-down"
  xmlns="http://www.w3.org/2000/svg"
  width="24"
  height="24"
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  stroke-width="2"
  stroke-linecap="round"
  stroke-linejoin="round"
  >
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M6 9l6 6l6 -6" />
</svg>                            </a>

                            <ul  class="tp-submenu">
            <li class="">
            <a href="https://shofy.botble.com"
               title="Electronics"
                           >
                

                Electronics

                            </a>

                    </li>
            <li class="">
            <a href="https://shofy-fashion.botble.com"
               title="Fashion"
                           >
                

                Fashion

                            </a>

                    </li>
            <li class="">
            <a href="https://shofy-beauty.botble.com"
               title="Beauty"
                           >
                

                Beauty

                            </a>

                    </li>
            <li class="">
            <a href="https://shofy-jewelry.botble.com"
               title="Jewelry"
                           >
                

                Jewelry

                            </a>

                    </li>
            <li class="">
            <a href="https://shofy-grocery.botble.com"
               title="Grocery"
                           >
                

                Grocery

                            </a>

                    </li>
    </ul>

                    </li>
            <li class="has-dropdown">
            <a href="http://127.0.0.1:8000"
               title="Shop"
                           >
                

                Shop

                                    <svg class="icon  svg-icon-ti-ti-chevron-down"
  xmlns="http://www.w3.org/2000/svg"
  width="24"
  height="24"
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  stroke-width="2"
  stroke-linecap="round"
  stroke-linejoin="round"
  >
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M6 9l6 6l6 -6" />
</svg>                            </a>

                            <ul  class="tp-submenu">
            <li class="">
            <a href="http://127.0.0.1:8000/categories"
               title="Shop Categories"
                           >
                

                Shop Categories

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/brands"
               title="Shop Brands"
                           >
                

                Shop Brands

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/products?layout=list"
               title="Shop List"
                           >
                

                Shop List

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/products?layout=grid"
               title="Shop Grid"
                           >
                

                Shop Grid

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000"
               title="Product Detail"
                           >
                

                Product Detail

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/coupons"
               title="Grab Coupons"
                           >
                

                Grab Coupons

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/cart"
               title="Cart"
                           >
                

                Cart

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/compare"
               title="Compare"
                           >
                

                Compare

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/wishlist"
               title="Wishlist"
                           >
                

                Wishlist

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/orders/tracking"
               title="Track Your Order"
                           >
                

                Track Your Order

                            </a>

                    </li>
    </ul>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/stores"
               title="Vendors"
                           >
                

                Vendors

                            </a>

                    </li>
            <li class="has-dropdown">
            <a href="http://127.0.0.1:8000"
               title="Pages"
                           >
                

                Pages

                                    <svg class="icon  svg-icon-ti-ti-chevron-down"
  xmlns="http://www.w3.org/2000/svg"
  width="24"
  height="24"
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  stroke-width="2"
  stroke-linecap="round"
  stroke-linejoin="round"
  >
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M6 9l6 6l6 -6" />
</svg>                            </a>

                            <ul  class="tp-submenu">
            <li class="">
            <a href="http://127.0.0.1:8000/faqs"
               title="FAQs"
                           >
                

                FAQs

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/login"
               title="Login"
                           >
                

                Login

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/register"
               title="Register"
                           >
                

                Register

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/password/reset"
               title="Forgot Password"
                           >
                

                Forgot Password

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/404"
               title="404 Error"
                           >
                

                404 Error

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/coming-soon"
               title="Coming Soon"
                           >
                

                Coming Soon

                            </a>

                    </li>
    </ul>

                    </li>
            <li class="has-dropdown">
            <a href="http://127.0.0.1:8000/blog"
               title="Blog"
                           >
                

                Blog

                                    <svg class="icon  svg-icon-ti-ti-chevron-down"
  xmlns="http://www.w3.org/2000/svg"
  width="24"
  height="24"
  viewBox="0 0 24 24"
  fill="none"
  stroke="currentColor"
  stroke-width="2"
  stroke-linecap="round"
  stroke-linejoin="round"
  >
  <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
  <path d="M6 9l6 6l6 -6" />
</svg>                            </a>

                            <ul  class="tp-submenu">
            <li class="">
            <a href="http://127.0.0.1:8000/blog?layout=grid"
               title="Blog Grid"
                           >
                

                Blog Grid

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/blog?layout=list"
               title="Blog List"
                           >
                

                Blog List

                            </a>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000"
               title="Blog Detail"
                           >
                

                Blog Detail

                            </a>

                    </li>
    </ul>

                    </li>
            <li class="">
            <a href="http://127.0.0.1:8000/contact"
               title="Contact"
                           >
                

                Contact

                            </a>

                    </li>
    </ul>
";