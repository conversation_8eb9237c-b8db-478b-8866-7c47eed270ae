version: '3'
services:
    app:
        image: ghcr.io/archielite/laravel:php8.1
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '${APP_PORT:-80}:80'
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        depends_on:
            - mysql
            - elasticsearch
    mysql:
        image: 'mysql/mysql-server:8.0'
        ports:
            - '${FORWARD_DB_PORT:-3306}:3306'
        environment:
            MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ROOT_HOST: "%"
            MYSQL_DATABASE: '${DB_DATABASE}'
            MYSQL_USER: '${DB_USERNAME}'
            MYSQL_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ALLOW_EMPTY_PASSWORD: 1
        volumes:
            - 'sail-mysql:/var/lib/mysql'
        networks:
            - sail
        healthcheck:
            test: ["C<PERSON>", "mysqladmin", "ping", "-p${DB_PASSWORD}"]
            retries: 3
            timeout: 5s
    elasticsearch:
        image: 'elasticsearch:8.11.0'
        ports:
            - '${ELASTICSEARCH_PORT:-9200}:9200'
        environment:
            - discovery.type=single-node
            - xpack.security.enabled=false
            - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
        volumes:
            - 'sail-elasticsearch:/usr/share/elasticsearch/data'
        networks:
            - sail
        healthcheck:
            test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
            retries: 5
            timeout: 10s
networks:
    sail:
        driver: bridge
volumes:
    sail-mysql:
        driver: local
    sail-elasticsearch:
        driver: local
