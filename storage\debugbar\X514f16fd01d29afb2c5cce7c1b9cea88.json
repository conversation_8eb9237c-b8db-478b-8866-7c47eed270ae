{"__meta": {"id": "X514f16fd01d29afb2c5cce7c1b9cea88", "datetime": "2025-11-29 17:15:32", "utime": 1764436532.765714, "method": "POST", "uri": "/ajax/render-ui-blocks", "ip": "127.0.0.1"}, "php": {"version": "8.2.29", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1764436532.151197, "end": 1764436532.765731, "duration": 0.6145341396331787, "duration_str": "615ms", "measures": [{"label": "Booting", "start": 1764436532.151197, "relative_start": 0, "end": 1764436532.672964, "relative_end": 1764436532.672964, "duration": 0.5217671394348145, "duration_str": "522ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1764436532.672974, "relative_start": 0.5217771530151367, "end": 1764436532.765734, "relative_end": 2.86102294921875e-06, "duration": 0.09275984764099121, "duration_str": "92.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52210728, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "theme.shofy::partials.shortcodes.blog-posts.index", "param_count": null, "params": [], "start": 1764436532.724961, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/blog-posts/index.blade.phptheme.shofy::partials.shortcodes.blog-posts.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fblog-posts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.section-title", "param_count": null, "params": [], "start": 1764436532.725754, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/section-title.blade.phptheme.shofy::partials.section-title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fsection-title.blade.php&line=1", "ajax": false, "filename": "section-title.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.section-title-inner", "param_count": null, "params": [], "start": 1764436532.726378, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/section-title-inner.blade.phptheme.shofy::partials.section-title-inner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fsection-title-inner.blade.php&line=1", "ajax": false, "filename": "section-title-inner.blade.php", "line": "?"}}, {"name": "__components::9413e731e9bb6e320e018067130903e9", "param_count": null, "params": [], "start": 1764436532.741361, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/9413e731e9bb6e320e018067130903e9.blade.php__components::9413e731e9bb6e320e018067130903e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F9413e731e9bb6e320e018067130903e9.blade.php&line=1", "ajax": false, "filename": "9413e731e9bb6e320e018067130903e9.blade.php", "line": "?"}}, {"name": "__components::9413e731e9bb6e320e018067130903e9", "param_count": null, "params": [], "start": 1764436532.75128, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/9413e731e9bb6e320e018067130903e9.blade.php__components::9413e731e9bb6e320e018067130903e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F9413e731e9bb6e320e018067130903e9.blade.php&line=1", "ajax": false, "filename": "9413e731e9bb6e320e018067130903e9.blade.php", "line": "?"}}, {"name": "__components::9413e731e9bb6e320e018067130903e9", "param_count": null, "params": [], "start": 1764436532.760182, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\storage\\framework\\views/9413e731e9bb6e320e018067130903e9.blade.php__components::9413e731e9bb6e320e018067130903e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fstorage%2Fframework%2Fviews%2F9413e731e9bb6e320e018067130903e9.blade.php&line=1", "ajax": false, "filename": "9413e731e9bb6e320e018067130903e9.blade.php", "line": "?"}}]}, "route": {"uri": "POST ajax/render-ui-blocks", "middleware": "Botble\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware, web, core, localeSessionRedirect, localizationRedirect", "controller": "Botble\\Shortcode\\Http\\Controllers\\ShortcodeController@ajaxRenderUiBlock", "namespace": null, "prefix": "/", "where": [], "as": "public.ajax.render-ui-block", "file": "<a href=\"phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fshortcode%2Fsrc%2FHttp%2FControllers%2FShortcodeController.php&line=57\" onclick=\"\">platform/packages/shortcode/src/Http/Controllers/ShortcodeController.php:57-74</a>"}, "queries": {"nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00263, "accumulated_duration_str": "2.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `posts` where `status` = 'published' order by `created_at` desc limit 3", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/blog/src/Repositories/Eloquent/PostRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\blog\\src\\Repositories\\Eloquent\\PostRepository.php", "line": 149}, {"index": 17, "namespace": null, "name": "platform/plugins/blog/helpers/helpers.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\blog\\helpers\\helpers.php", "line": 71}, {"index": 23, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 24, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}], "start": 1764436532.7093332, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 0, "width_percent": 37.262}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (16, 17, 18) and `slugs`.`reference_type` = 'Botble\\\\Blog\\\\Models\\\\Post'", "type": "query", "params": [], "bindings": ["Botble\\Blog\\Models\\Post"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/plugins/blog/src/Repositories/Eloquent/PostRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\blog\\src\\Repositories\\Eloquent\\PostRepository.php", "line": 149}, {"index": 23, "namespace": null, "name": "platform/plugins/blog/helpers/helpers.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\blog\\helpers\\helpers.php", "line": 71}, {"index": 29, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}], "start": 1764436532.713871, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 37.262, "width_percent": 19.772}, {"sql": "select `categories`.*, `post_categories`.`post_id` as `pivot_post_id`, `post_categories`.`category_id` as `pivot_category_id` from `categories` inner join `post_categories` on `categories`.`id` = `post_categories`.`category_id` where `post_categories`.`post_id` in (16, 17, 18)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-blog.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-blog.php", "line": 36}, {"index": 26, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 28, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}, {"index": 30, "namespace": null, "name": "platform/packages/shortcode/src/Http/Controllers/ShortcodeController.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Http\\Controllers\\ShortcodeController.php", "line": 69}], "start": 1764436532.718354, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "shortcodes-blog.php:36", "source": {"index": 21, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-blog.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-blog.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Ffunctions%2Fshortcodes-blog.php&line=36", "ajax": false, "filename": "shortcodes-blog.php", "line": "36"}, "connection": "shofy", "explain": null, "start_percent": 57.034, "width_percent": 26.996}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (2, 3, 4, 5) and `slugs`.`reference_type` = 'Botble\\\\Blog\\\\Models\\\\Category'", "type": "query", "params": [], "bindings": ["Botble\\Blog\\Models\\Category"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-blog.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-blog.php", "line": 36}, {"index": 29, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 30, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 31, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}], "start": 1764436532.7215068, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 84.03, "width_percent": 15.97}]}, "models": {"data": {"Botble\\Slug\\Models\\Slug": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Blog\\Models\\Category": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fblog%2Fsrc%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Botble\\Blog\\Models\\Post": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fblog%2Fsrc%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}}, "count": 14, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "language": "en"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/a0797d46-c4bc-43aa-84a9-60371f313c73\" target=\"_blank\">View in Telescope</a>", "path_info": "/ajax/render-ui-blocks", "status_code": "<pre class=sf-dump id=sf-dump-395358760 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-395358760\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1920624646 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1920624646\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-141368215 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">blog-posts</span>\"\n  \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Latest news &amp; articles</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">latest</span>\"\n    \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>3</span>\"\n    \"<span class=sf-dump-key>button_label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">View All</span>\"\n    \"<span class=sf-dump-key>button_url</span>\" => \"<span class=sf-dump-str title=\"5 characters\">/blog</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-141368215\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-757565212 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">187</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;142&quot;, &quot;Google Chrome&quot;;v=&quot;142&quot;, &quot;Not_A Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/142.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1957 characters\">botble_footprints_cookie=eyJpdiI6ImRFeFg2U3EwYUtoR05hRi9RVWdmN0E9PSIsInZhbHVlIjoiVk55ay9yby9GSzFqbEIzV2tzLzJtQis3OHJPWURuN014SmtndlQ4ZmhwN0JSNWdQMmxXVG9abmdrVGIwcENEdDVxc2tvWWlMK0s1WlBXa29wK1IzZUZWeHV6aGE0MTB3UU1YYy9OK0JYcXgvdUw3WGxVVGVWbVVkcStwbFBIWWEiLCJtYWMiOiJlMmI4MjIyMWM3NGI5OWUxYTU3M2RiMDlkNmIxZDE5ODE3NmIxNDJhNzJjODFiYjllYTk2NjY4NDUzMWZhNGUwIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IkZ6Z1d1ZjZtRlhZdy80MFJxOER2T0E9PSIsInZhbHVlIjoibDhQbEMyOStBTlQ5czkzYWRIMGczRDhRMEFVMWNYOVQ1eU0zZ1hYVlp2VWhGUVZmYlYxTksrS0I4aWI3b0FMTXRDd2sybTRGSFZwRGEzN0JTQzZMWFF0S0wwMndURVhIRm51UFQvVElkSFR6dmlBWjRKMVROamc4dW9HTGJselFDTlZQNC9qeDdNOXJvd0djRmN1anc3UHdSL2p1cE9RZGxROEc0SE15elhQUFJ4Q2Y3ODhycmVoM1EwNUJaQTRCY0VXVzNiUmNnS21GM1hWMnJwYUNXWHlIUFd6cmlYRlBnNnMwY3lpK0MzR21lUjRtSm1NZHRPbmUvR2szQngwdW4wZUhVM1Z4NC9PVGxJdG9pVWdPK2RZTXF4N1lUWjR3ZndTeGVFKzZVTmE0RHI4cWp2akJJaEo4WmZWc0s1Y0kwdWx1TGNEeW45ZEJzbDZNeWtadXJKMi9OOHJTcUZrVEIrRTBRa1RNZjFNUVJyaHpWYzdObHpjNU5zMUtvVlVQUlN6amt1aEcwR25iTWhSdFpYcG1jZGFmS0QyN01JVTdpcmtBeDVSQ1BpaVFxbjhBMVZtNEo2eGxGL2lpcjJiT1JreWdFQ3FkcFZraDg3MUNqU253ZmJGck9aSGc0NzNOVWpOUjBoNzU1VFE9IiwibWFjIjoiMTE0ZjdmM2EzODVjMWUxYzFmYzgwNmZhNWEwYzZjYTQwOTc2ODVkNTk1NDZkZGE0MzAwODc5OGMyNzg4ODc2ZCIsInRhZyI6IiJ9; newsletter_popup=1; XSRF-TOKEN=eyJpdiI6InlhTnZCVmFEV0s2R0xqM1RjNGxrNmc9PSIsInZhbHVlIjoidmdPUit2MEpGbklpVHh0SlJmdnZGM0xuTXlPQlV2WnJOeTVUbEtyRHh1RXRPVFhQcmkvWFVjNnlsdU5reGpWQmFOeXZDU0VzVnk2cHcreVhCaDVqY2Z1N0lWWjNoTjdWbDM1cWwreEI4L3A5YlQ1V3J1bzZvUHpDVGNRdnBzUkMiLCJtYWMiOiI3MmJlZWNhYjcyNzczMDZmOWFkMGU4NzU1MjZlODZmZmQ3MDE4YzRhODY0MTcyYjA0ZTg2YzNlZWVjZGViNWEzIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IjdiL2pPVnRZY0F5WC8rc3c1TUpFTGc9PSIsInZhbHVlIjoiM3BhajdFRWZ3MTBLNjk2OVM1MU01TWxYSzgzQ1g2SVBiN2EvWlRVU0kwQTlzb1lxMUFRMFdkTlcyNGI4L3Ricjh6UTV2bkozMU8zVVQveDQ1OXhMMzRFYmpIenhrTW9SanJCZitvSGVwZjJkZHNFZmp4UU9rR2Y5c2pFdGx2U2ciLCJtYWMiOiJmZjNkZTA1YzBhMjRkM2ZkMjZiZmE1Nzc2MGZiMWExNTYyNDc1YjA0OTBkODY2NWFlOGQ0NzFjM2E0NDQxMGY5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757565212\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-759637958 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5832e034bf28c6f97be3a608bb8c655f40381a49</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"320 characters\">{&quot;footprint&quot;:&quot;5832e034bf28c6f97be3a608bb8c655f40381a49&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;127.0.0.1&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>newsletter_popup</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PMPD3sRy7YrBqKNvMw96LkySo4AQ8DJuqi1a8JCS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759637958\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-561563161 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 29 Nov 2025 17:15:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InBhK1AyMWY3NHpnMXl1QW9Henk4WUE9PSIsInZhbHVlIjoib2tqcURiMmJXOHpjYnVzRkx5bytRcGh3NGNYVE5uNVRvQ0MzUE1wc1c4eVZSbXcyM0tXNTFSakxWUXFXZndMU2psVXpWNEdpWHliYTBpRUhQK0NIZm1KZHVPbEs0eDVBejhudWVLM1J3eDB1MDA2Y29oSDNKWTBLV3piRVJXT0ciLCJtYWMiOiJmZTk0NmZjYWYxMjI3YWFkYTRmMWYyMTc3YmY2NzQ1NDk1OGY2ZmZkYmNlN2RmZGNlZTY5ODk5NGJjYTE2MzEyIiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:15:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">botble_session=eyJpdiI6IkQybDRZeTEwVndncXNOb3BFL3BQSWc9PSIsInZhbHVlIjoiOCtnQ3VLQVFlRDNURFhIUnFQcEp3aGx3VmhxQ1ZKZXVsWXl1SlNVaGwrT1hpMzdQaWQvMzZCblhhSUdSTG5KMEhSeFpOOW10NDQ3cUg4RUpyR3RRS05iQm9TMW0vR1gwWEdsYUlxK3lUVTZzbVhXMXJsSTA1U0tZVm4rNzBYUHEiLCJtYWMiOiI0YmYzNjMyNjQyZWQ5MTc2MzYwMDRjYWMzN2FkZDIxODVmZDY4NGIzNmQ3MWEyZDM4MmVlOWJiMTZjNjljZjFmIiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:15:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InBhK1AyMWY3NHpnMXl1QW9Henk4WUE9PSIsInZhbHVlIjoib2tqcURiMmJXOHpjYnVzRkx5bytRcGh3NGNYVE5uNVRvQ0MzUE1wc1c4eVZSbXcyM0tXNTFSakxWUXFXZndMU2psVXpWNEdpWHliYTBpRUhQK0NIZm1KZHVPbEs0eDVBejhudWVLM1J3eDB1MDA2Y29oSDNKWTBLV3piRVJXT0ciLCJtYWMiOiJmZTk0NmZjYWYxMjI3YWFkYTRmMWYyMTc3YmY2NzQ1NDk1OGY2ZmZkYmNlN2RmZGNlZTY5ODk5NGJjYTE2MzEyIiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:15:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">botble_session=eyJpdiI6IkQybDRZeTEwVndncXNOb3BFL3BQSWc9PSIsInZhbHVlIjoiOCtnQ3VLQVFlRDNURFhIUnFQcEp3aGx3VmhxQ1ZKZXVsWXl1SlNVaGwrT1hpMzdQaWQvMzZCblhhSUdSTG5KMEhSeFpOOW10NDQ3cUg4RUpyR3RRS05iQm9TMW0vR1gwWEdsYUlxK3lUVTZzbVhXMXJsSTA1U0tZVm4rNzBYUHEiLCJtYWMiOiI0YmYzNjMyNjQyZWQ5MTc2MzYwMDRjYWMzN2FkZDIxODVmZDY4NGIzNmQ3MWEyZDM4MmVlOWJiMTZjNjljZjFmIiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:15:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-561563161\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-592371378 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-592371378\", {\"maxDepth\":0})</script>\n"}}