<?php
    Theme::set('pageTitle', $page->name);

    if ($breadcrumbStyle = $page->getMetaData('breadcrumb_style', true)) {
        Theme::set('breadcrumbStyle', $breadcrumbStyle);
    }

    if ($breadcrumbBackground = $page->getMetaData('breadcrumb_background', true)) {
        Theme::set('breadcrumbBackground', $breadcrumbBackground);
    }

    Theme::set('isHomePage', BaseHelper::isHomePage($page->id));
?>

<?php if(BaseHelper::isHomepage($page->id)): ?>
    <?php echo apply_filters(PAGE_FILTER_FRONT_PAGE_CONTENT, BaseHelper::clean($page->content), $page); ?>

<?php else: ?>
    <div class="ck-content">
        <?php echo apply_filters(PAGE_FILTER_FRONT_PAGE_CONTENT, BaseHelper::clean($page->content), $page); ?>

    </div>
<?php endif; ?>
<?php /**PATH D:\hassan code\shofy\platform\themes/shofy/views/page.blade.php ENDPATH**/ ?>