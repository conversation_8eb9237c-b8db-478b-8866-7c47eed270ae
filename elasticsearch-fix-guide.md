# Elasticsearch Configuration Fix Guide

## Option 1: Install and Configure Elasticsearch

### Step 1: Install Elasticsearch
Download and install Elasticsearch from: https://www.elastic.co/downloads/elasticsearch

### Step 2: Start Elasticsearch Service
```bash
# On Windows (if installed as service)
net start elasticsearch

# Or run directly
bin\elasticsearch.bat
```

### Step 3: Update .env Configuration
```env
# Use HTTP instead of HTTPS for local development
SCOUT_DRIVER=elastic
ELASTICSEARCH_HOST=127.0.0.1
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_SCHEME=http
ELASTICSEARCH_USER=
ELASTICSEARCH_PASS=
ELASTICSEARCH_SSL_VERIFICATION=false
```

### Step 4: Index Your Products
```bash
php artisan scout:import "Botble\Ecommerce\Models\Product"
```

## Option 2: Use Database Search (Current Fix)

Keep the current configuration:
```env
SCOUT_DRIVER=database
```

This uses <PERSON><PERSON>'s database-based search which is simpler but less powerful than Elasticsearch.

## Option 3: Use Alternative Search Engines

### Meilisearch (Recommended Alternative)
```env
SCOUT_DRIVER=meilisearch
MEILISEARCH_HOST=http://127.0.0.1:7700
```

### Algolia (Cloud-based)
```env
SCOUT_DRIVER=algolia
ALGOLIA_APP_ID=your_app_id
ALGOLIA_SECRET=your_secret_key
```

## PHP Version Issue

Your application requires PHP 8.1+ but you're running PHP 7.4. Consider upgrading PHP:

1. Download PHP 8.1+ from: https://windows.php.net/download/
2. Update your web server configuration
3. Update composer dependencies

## Testing the Fix

After making changes, test by:
1. Accessing your product pages
2. Using search functionality
3. Creating/updating products

The "No alive nodes" error should be resolved with the database driver.
