<?php

/**
 * Simple test script to verify AJAX endpoints
 * Run this from the command line: php test_ajax_endpoints.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Test GET endpoints
function testGetEndpoint($url) {
    echo "Testing GET: $url\n";
    
    $request = Request::create($url, 'GET');
    $request->headers->set('Accept', 'application/json');
    $request->headers->set('X-Requested-With', 'XMLHttpRequest');
    
    try {
        global $kernel;
        $response = $kernel->handle($request);
        
        echo "Status: " . $response->getStatusCode() . "\n";
        
        if ($response->getStatusCode() === 200) {
            $data = json_decode($response->getContent(), true);
            
            if (isset($data['data'])) {
                if (isset($data['data']['count'])) {
                    echo "Count: " . $data['data']['count'] . "\n";
                }
                if (isset($data['data']['html'])) {
                    echo "HTML Length: " . strlen($data['data']['html']) . " characters\n";
                }
                if (isset($data['data']['select'])) {
                    echo "Select HTML Length: " . strlen($data['data']['select']) . " characters\n";
                }
                if (isset($data['data']['dropdown'])) {
                    echo "Dropdown HTML Length: " . strlen($data['data']['dropdown'] ?? '') . " characters\n";
                }
                echo "Error: " . ($data['error'] ? 'true' : 'false') . "\n";
            } else {
                echo "Response: " . substr($response->getContent(), 0, 200) . "...\n";
            }
        } else {
            echo "Error Response: " . $response->getContent() . "\n";
        }
        
        echo "---\n";
        
    } catch (Exception $e) {
        echo "Exception: " . $e->getMessage() . "\n";
        echo "---\n";
    }
}

// Test POST endpoints
function testPostEndpoint($url, $data) {
    echo "Testing POST: $url\n";
    echo "Data: " . json_encode($data) . "\n";
    
    $request = Request::create($url, 'POST', $data);
    $request->headers->set('Accept', 'application/json');
    $request->headers->set('X-Requested-With', 'XMLHttpRequest');
    $request->headers->set('Content-Type', 'application/json');
    
    try {
        global $kernel;
        $response = $kernel->handle($request);
        
        echo "Status: " . $response->getStatusCode() . "\n";
        
        if ($response->getStatusCode() === 200) {
            $responseData = json_decode($response->getContent(), true);
            
            if (isset($responseData['data'])) {
                echo "Response Data Length: " . strlen($responseData['data'] ?? '') . " characters\n";
                echo "Error: " . ($responseData['error'] ? 'true' : 'false') . "\n";
            } else {
                echo "Response: " . substr($response->getContent(), 0, 200) . "...\n";
            }
        } else {
            echo "Error Response: " . $response->getContent() . "\n";
        }
        
        echo "---\n";
        
    } catch (Exception $e) {
        echo "Exception: " . $e->getMessage() . "\n";
        echo "---\n";
    }
}

// Test different endpoints
echo "=== AJAX Endpoints Tests ===\n\n";

echo "=== 1. AJAX Products Endpoint Tests ===\n\n";
testGetEndpoint('/ajax/products?limit=8&type=all');
testGetEndpoint('/ajax/products?limit=5&type=featured');
testGetEndpoint('/ajax/products?limit=3&type=trending');
testGetEndpoint('/ajax/products?limit=4&type=on-sale');
testGetEndpoint('/ajax/products?limit=6&type=top-rated');

echo "\n=== 2. AJAX Products with Search ===\n\n";
testGetEndpoint('/ajax/products?q=test&limit=5');

echo "\n=== 3. AJAX Categories Dropdown Tests ===\n\n";
testGetEndpoint('/ajax/categories-dropdown');

echo "\n=== 4. AJAX Render UI Blocks Tests ===\n\n";
// Test with a common shortcode
testPostEndpoint('/ajax/render-ui-blocks', [
    'name' => 'site-features',
    'attributes' => [
        'title' => 'Test Features'
    ]
]);

// Test with Google Maps shortcode
testPostEndpoint('/ajax/render-ui-blocks', [
    'name' => 'google-map',
    'attributes' => [
        'width' => '100%',
        'height' => '400'
    ]
]);

// Test with invalid shortcode
testPostEndpoint('/ajax/render-ui-blocks', [
    'name' => 'non-existent-shortcode',
    'attributes' => []
]);

echo "\n=== 5. Cache Performance Tests ===\n\n";

echo "Products Cache Test:\n";
$start = microtime(true);
testGetEndpoint('/ajax/products?limit=8&type=all');
$time1 = microtime(true) - $start;
echo "First request time: " . round($time1 * 1000, 2) . "ms\n\n";

$start = microtime(true);
testGetEndpoint('/ajax/products?limit=8&type=all');
$time2 = microtime(true) - $start;
echo "Second request time: " . round($time2 * 1000, 2) . "ms\n";
echo ($time2 < $time1 ? "✓ Cached request was faster\n" : "⚠ Cache may not be working\n");

echo "\nCategories Cache Test:\n";
$start = microtime(true);
testGetEndpoint('/ajax/categories-dropdown');
$time1 = microtime(true) - $start;
echo "First request time: " . round($time1 * 1000, 2) . "ms\n\n";

$start = microtime(true);
testGetEndpoint('/ajax/categories-dropdown');
$time2 = microtime(true) - $start;
echo "Second request time: " . round($time2 * 1000, 2) . "ms\n";
echo ($time2 < $time1 ? "✓ Cached request was faster\n" : "⚠ Cache may not be working\n");

echo "\nTests completed!\n";
