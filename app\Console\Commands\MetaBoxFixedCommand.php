<?php

namespace App\Console\Commands;

use Botble\Base\Models\MetaBox;
use Botble\Ecommerce\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MetaBoxFixedCommand extends Command
{
    protected $signature = 'fixed:meta-box';
    protected $description = 'Fixed meta box';

    public function handle()
    {
        Product::query()->chunk(500, function ($products) {
            foreach ($products as $product) {
                $this->info("Processing product: {$product->name}");
                
                if ($product->is_variation != 1 || empty($product->name)) {
                    continue;
                }

                try {
                    $this->processProductMetaBoxes($product);
                } catch (\Exception $e) {
                    $this->error("Failed to process product #{$product->id}: {$e->getMessage()}");
                }
            }
        });
    }

    private function processProductMetaBoxes(Product $product): void
    {
        $localization = DB::table('ec_products_translations')
            ->where('ec_products_id', $product->id)
            ->first();

        // Process Arabic meta box
        $this->processArabicMetaBox($product);
        
        // Process English meta box
        $this->processEnglishMetaBox($product, $localization);
    }

    private function processArabicMetaBox(Product $product): void
    {
        $metaBoxAr = MetaBox::query()
            ->where('meta_key', 'seo_meta')
            ->where('reference_id', $product->id)
            ->first();

        $metaValue = [
            'seo_title' => trim($product->name . " في الاردن"),
            'seo_description' => $this->html_to_text_trim($product->description),
            'seo_image' => $product->image,
            'index' => 'index',
        ];

        if ($metaBoxAr) {
            $metaBoxAr->meta_value = $metaValue;
            $metaBoxAr->saveQuietly();
            $this->info("Updated Arabic meta box #{$metaBoxAr->id}");
        } else {
            $metaBoxAr = new MetaBox();
            $metaBoxAr->meta_key = 'seo_meta';
            $metaBoxAr->reference_id = $product->id;
            $metaBoxAr->reference_type = Product::class;
            $metaBoxAr->meta_value = $metaValue;
            $metaBoxAr->save();
            $this->info("Created Arabic meta box #{$metaBoxAr->id}");
        }
    }

    private function processEnglishMetaBox(Product $product, $localization): void
    {
        $metaBoxEn = MetaBox::query()
            ->where('meta_key', 'en_US_seo_meta')
            ->where('reference_id', $product->id)
            ->first();

        $name = $localization->name ?? $product->name;
        $description = $localization->description ?? $product->description;

        $metaValue = [
            'seo_title' => trim($name . " in Jordan"),
            'seo_description' => $this->html_to_text_trim($description),
            'seo_image' => $product->image,
            'index' => 'index',
        ];

        if ($metaBoxEn) {
            $metaBoxEn->meta_value = $metaValue;
            $metaBoxEn->saveQuietly();
            $this->info("Updated English meta box #{$metaBoxEn->id}");
        } else {
            $metaBoxEn = new MetaBox();
            $metaBoxEn->meta_key = 'en_US_seo_meta';
            $metaBoxEn->reference_id = $product->id;
            $metaBoxEn->reference_type = Product::class;
            $metaBoxEn->meta_value = $metaValue;
            $metaBoxEn->save();
            $this->info("Created English meta box #{$metaBoxEn->id}");
        }
    }

    public function html_to_text_trim(string $html, int $limit = 250): string
    {
        // Remove scripts and styles
        $html = preg_replace('#<script(.*?)>(.*?)</script>#is', '', $html);
        $html = preg_replace('#<style(.*?)>(.*?)</style>#is', '', $html);

        // Convert HTML entities and strip tags
        $text = html_entity_decode($html, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        $text = strip_tags($text);

        // Compress whitespace and trim
        $text = preg_replace('/\s+/u', ' ', $text);
        $text = trim($text);

        // Truncate with ellipsis if needed
        if (mb_strlen($text, 'UTF-8') > $limit) {
            return mb_substr($text, 0, $limit - 3, 'UTF-8') . '...';
        }

        return $text;
    }
}