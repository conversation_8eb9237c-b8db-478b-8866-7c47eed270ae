{"__meta": {"id": "Xfc04bb297fa78bc620e1233ca61d8ac2", "datetime": "2025-11-29 17:08:38", "utime": 1764436118.175684, "method": "POST", "uri": "/ajax/render-ui-blocks", "ip": "127.0.0.1"}, "php": {"version": "8.2.29", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1764436117.283103, "end": 1764436118.175738, "duration": 0.8926351070404053, "duration_str": "893ms", "measures": [{"label": "Booting", "start": 1764436117.283103, "relative_start": 0, "end": 1764436117.866802, "relative_end": 1764436117.866802, "duration": 0.5836989879608154, "duration_str": "584ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1764436117.866816, "relative_start": 0.5837130546569824, "end": 1764436118.175745, "relative_end": 6.9141387939453125e-06, "duration": 0.3089289665222168, "duration_str": "309ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 55296888, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 44, "templates": [{"name": "theme.shofy::partials.shortcodes.ecommerce-flash-sale.index", "param_count": null, "params": [], "start": 1764436117.956176, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-flash-sale/index.blade.phptheme.shofy::partials.shortcodes.ecommerce-flash-sale.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fecommerce-flash-sale%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.shortcodes.ecommerce-flash-sale.style-1", "param_count": null, "params": [], "start": 1764436117.956795, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/shortcodes/ecommerce-flash-sale/style-1.blade.phptheme.shofy::partials.shortcodes.ecommerce-flash-sale.style-1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fshortcodes%2Fecommerce-flash-sale%2Fstyle-1.blade.php&line=1", "ajax": false, "filename": "style-1.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.section-title", "param_count": null, "params": [], "start": 1764436117.957418, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/section-title.blade.phptheme.shofy::partials.section-title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fsection-title.blade.php&line=1", "ajax": false, "filename": "section-title.blade.php", "line": "?"}}, {"name": "theme.shofy::partials.section-title-inner", "param_count": null, "params": [], "start": 1764436117.958074, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/partials/section-title-inner.blade.phptheme.shofy::partials.section-title-inner", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fpartials%2Fsection-title-inner.blade.php&line=1", "ajax": false, "filename": "section-title-inner.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product-item", "param_count": null, "params": [], "start": 1764436117.960835, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product-item.blade.phptheme.shofy::views.ecommerce.includes.product-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct-item.blade.php&line=1", "ajax": false, "filename": "product-item.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.grid", "param_count": null, "params": [], "start": 1764436117.962459, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/grid.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.badges", "param_count": null, "params": [], "start": 1764436117.978241, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/badges.blade.phptheme.shofy::views.ecommerce.includes.product.badges", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fbadges.blade.php&line=1", "ajax": false, "filename": "badges.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "param_count": null, "params": [], "start": 1764436117.999238, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.rating", "param_count": null, "params": [], "start": 1764436118.025898, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/rating.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.rating", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Frating.blade.php&line=1", "ajax": false, "filename": "rating.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.rating-star", "param_count": null, "params": [], "start": 1764436118.027319, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/rating-star.blade.phpplugins/ecommerce::themes.includes.rating-star", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Frating-star.blade.php&line=1", "ajax": false, "filename": "rating-star.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.price", "param_count": null, "params": [], "start": 1764436118.030006, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/price.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fprice.blade.php&line=1", "ajax": false, "filename": "price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-price", "param_count": null, "params": [], "start": 1764436118.031359, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-price.blade.phpplugins/ecommerce::themes.includes.product-price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-price.blade.php&line=1", "ajax": false, "filename": "product-price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-prices.original", "param_count": null, "params": [], "start": 1764436118.042092, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-prices/original.blade.phpplugins/ecommerce::themes.includes.product-prices.original", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-prices%2Foriginal.blade.php&line=1", "ajax": false, "filename": "original.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.countdown", "param_count": null, "params": [], "start": 1764436118.044281, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/countdown.blade.phptheme.shofy::views.ecommerce.includes.product.countdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fcountdown.blade.php&line=1", "ajax": false, "filename": "countdown.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product-item", "param_count": null, "params": [], "start": 1764436118.045466, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product-item.blade.phptheme.shofy::views.ecommerce.includes.product-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct-item.blade.php&line=1", "ajax": false, "filename": "product-item.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.grid", "param_count": null, "params": [], "start": 1764436118.046371, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/grid.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.badges", "param_count": null, "params": [], "start": 1764436118.049259, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/badges.blade.phptheme.shofy::views.ecommerce.includes.product.badges", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fbadges.blade.php&line=1", "ajax": false, "filename": "badges.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "param_count": null, "params": [], "start": 1764436118.052713, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.rating", "param_count": null, "params": [], "start": 1764436118.069287, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/rating.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.rating", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Frating.blade.php&line=1", "ajax": false, "filename": "rating.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.rating-star", "param_count": null, "params": [], "start": 1764436118.070866, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/rating-star.blade.phpplugins/ecommerce::themes.includes.rating-star", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Frating-star.blade.php&line=1", "ajax": false, "filename": "rating-star.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.price", "param_count": null, "params": [], "start": 1764436118.073138, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/price.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fprice.blade.php&line=1", "ajax": false, "filename": "price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-price", "param_count": null, "params": [], "start": 1764436118.073996, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-price.blade.phpplugins/ecommerce::themes.includes.product-price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-price.blade.php&line=1", "ajax": false, "filename": "product-price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-prices.original", "param_count": null, "params": [], "start": 1764436118.079725, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-prices/original.blade.phpplugins/ecommerce::themes.includes.product-prices.original", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-prices%2Foriginal.blade.php&line=1", "ajax": false, "filename": "original.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.countdown", "param_count": null, "params": [], "start": 1764436118.081018, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/countdown.blade.phptheme.shofy::views.ecommerce.includes.product.countdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fcountdown.blade.php&line=1", "ajax": false, "filename": "countdown.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product-item", "param_count": null, "params": [], "start": 1764436118.081794, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product-item.blade.phptheme.shofy::views.ecommerce.includes.product-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct-item.blade.php&line=1", "ajax": false, "filename": "product-item.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.grid", "param_count": null, "params": [], "start": 1764436118.082538, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/grid.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.badges", "param_count": null, "params": [], "start": 1764436118.085909, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/badges.blade.phptheme.shofy::views.ecommerce.includes.product.badges", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fbadges.blade.php&line=1", "ajax": false, "filename": "badges.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "param_count": null, "params": [], "start": 1764436118.087469, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.rating", "param_count": null, "params": [], "start": 1764436118.106927, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/rating.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.rating", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Frating.blade.php&line=1", "ajax": false, "filename": "rating.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.rating-star", "param_count": null, "params": [], "start": 1764436118.108287, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/rating-star.blade.phpplugins/ecommerce::themes.includes.rating-star", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Frating-star.blade.php&line=1", "ajax": false, "filename": "rating-star.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.price", "param_count": null, "params": [], "start": 1764436118.110074, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/price.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fprice.blade.php&line=1", "ajax": false, "filename": "price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-price", "param_count": null, "params": [], "start": 1764436118.110691, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-price.blade.phpplugins/ecommerce::themes.includes.product-price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-price.blade.php&line=1", "ajax": false, "filename": "product-price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-prices.original", "param_count": null, "params": [], "start": 1764436118.115991, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-prices/original.blade.phpplugins/ecommerce::themes.includes.product-prices.original", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-prices%2Foriginal.blade.php&line=1", "ajax": false, "filename": "original.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.countdown", "param_count": null, "params": [], "start": 1764436118.117235, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/countdown.blade.phptheme.shofy::views.ecommerce.includes.product.countdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fcountdown.blade.php&line=1", "ajax": false, "filename": "countdown.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product-item", "param_count": null, "params": [], "start": 1764436118.11797, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product-item.blade.phptheme.shofy::views.ecommerce.includes.product-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct-item.blade.php&line=1", "ajax": false, "filename": "product-item.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.grid", "param_count": null, "params": [], "start": 1764436118.118743, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/grid.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.grid", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.badges", "param_count": null, "params": [], "start": 1764436118.122391, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/badges.blade.phptheme.shofy::views.ecommerce.includes.product.badges", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fbadges.blade.php&line=1", "ajax": false, "filename": "badges.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "param_count": null, "params": [], "start": 1764436118.126069, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.rating", "param_count": null, "params": [], "start": 1764436118.151607, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/rating.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.rating", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Frating.blade.php&line=1", "ajax": false, "filename": "rating.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.rating-star", "param_count": null, "params": [], "start": 1764436118.153844, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/rating-star.blade.phpplugins/ecommerce::themes.includes.rating-star", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Frating-star.blade.php&line=1", "ajax": false, "filename": "rating-star.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.style-1.price", "param_count": null, "params": [], "start": 1764436118.157694, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/price.blade.phptheme.shofy::views.ecommerce.includes.product.style-1.price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fstyle-1%2Fprice.blade.php&line=1", "ajax": false, "filename": "price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-price", "param_count": null, "params": [], "start": 1764436118.158713, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-price.blade.phpplugins/ecommerce::themes.includes.product-price", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-price.blade.php&line=1", "ajax": false, "filename": "product-price.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::themes.includes.product-prices.original", "param_count": null, "params": [], "start": 1764436118.165793, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-prices/original.blade.phpplugins/ecommerce::themes.includes.product-prices.original", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fthemes%2Fincludes%2Fproduct-prices%2Foriginal.blade.php&line=1", "ajax": false, "filename": "original.blade.php", "line": "?"}}, {"name": "theme.shofy::views.ecommerce.includes.product.countdown", "param_count": null, "params": [], "start": 1764436118.167735, "type": "blade", "hash": "bladeD:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/countdown.blade.phptheme.shofy::views.ecommerce.includes.product.countdown", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fthemes%2Fshofy%2Fviews%2Fecommerce%2Fincludes%2Fproduct%2Fcountdown.blade.php&line=1", "ajax": false, "filename": "countdown.blade.php", "line": "?"}}]}, "route": {"uri": "POST ajax/render-ui-blocks", "middleware": "Botble\\Base\\Http\\Middleware\\RequiresJsonRequestMiddleware, web, core, localeSessionRedirect, localizationRedirect", "controller": "Botble\\Shortcode\\Http\\Controllers\\ShortcodeController@ajaxRenderUiBlock", "namespace": null, "prefix": "/", "where": [], "as": "public.ajax.render-ui-block", "file": "<a href=\"phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fshortcode%2Fsrc%2FHttp%2FControllers%2FShortcodeController.php&line=57\" onclick=\"\">platform/packages/shortcode/src/Http/Controllers/ShortcodeController.php:57-74</a>"}, "queries": {"nb_statements": 27, "nb_visible_statements": 27, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02829, "accumulated_duration_str": "28.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-11-29' and `id` = '1' and `status` = 'published' limit 1", "type": "query", "params": [], "bindings": ["2025-11-29", "1", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 22, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 23, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 24, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}], "start": 1764436117.9052799, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 0, "width_percent": 3.747}, {"sql": "select `ec_products`.*, (select avg(`ec_reviews`.`star`) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_avg`, (select count(*) from `ec_reviews` where `ec_products`.`id` = `ec_reviews`.`product_id` and `status` = 'published') as `reviews_count`, `ec_flash_sale_products`.`flash_sale_id` as `pivot_flash_sale_id`, `ec_flash_sale_products`.`product_id` as `pivot_product_id`, `ec_flash_sale_products`.`price` as `pivot_price`, `ec_flash_sale_products`.`quantity` as `pivot_quantity`, `ec_flash_sale_products`.`sold` as `pivot_sold` from `ec_products` inner join `ec_flash_sale_products` on `ec_products`.`id` = `ec_flash_sale_products`.`product_id` where `ec_flash_sale_products`.`flash_sale_id` in (1) and `status` = 'published' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 4", "type": "query", "params": [], "bindings": ["published", "published", "published", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 26, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 27, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 28, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}], "start": 1764436117.915031, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 3.747, "width_percent": 8.059}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1, 2, 3, 4) and `slugs`.`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 32, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 33, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}], "start": 1764436117.922109, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 11.806, "width_percent": 3.782}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`is_default` = 1 and `ec_product_variations`.`configurable_product_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 32, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 33, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}], "start": 1764436117.932627, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 15.589, "width_percent": 4.843}, {"sql": "select `ec_product_collections`.*, `ec_product_collection_products`.`product_id` as `pivot_product_id`, `ec_product_collection_products`.`product_collection_id` as `pivot_product_collection_id` from `ec_product_collections` inner join `ec_product_collection_products` on `ec_product_collections`.`id` = `ec_product_collection_products`.`product_collection_id` where `ec_product_collection_products`.`product_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 30, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 32, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}], "start": 1764436117.9376879, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 23, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 20.431, "width_percent": 5.232}, {"sql": "select `ec_product_labels`.*, `ec_product_label_products`.`product_id` as `pivot_product_id`, `ec_product_label_products`.`product_label_id` as `pivot_product_label_id` from `ec_product_labels` inner join `ec_product_label_products` on `ec_product_labels`.`id` = `ec_product_label_products`.`product_label_id` where `ec_product_label_products`.`product_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 30, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 31, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}, {"index": 32, "namespace": null, "name": "platform/packages/shortcode/src/Shortcode.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Shortcode.php", "line": 50}], "start": 1764436117.942627, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 23, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 25.663, "width_percent": 5.055}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` in (6, 7, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 32, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}, {"index": 33, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 101}], "start": 1764436117.947001, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 30.718, "width_percent": 4.242}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (6, 7, 8) and `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store'", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 31, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "platform/themes/shofy/functions/shortcodes-ecommerce.php", "file": "D:\\hassan code\\shofy\\platform\\themes\\shofy\\functions\\shortcodes-ecommerce.php", "line": 237}, {"index": 38, "namespace": null, "name": "platform/packages/shortcode/src/Compilers/ShortcodeCompiler.php", "file": "D:\\hassan code\\shofy\\platform\\packages\\shortcode\\src\\Compilers\\ShortcodeCompiler.php", "line": 121}], "start": 1764436117.9502099, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 34.959, "width_percent": 2.58}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-11-29' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-11-29", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 25}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": 1764436117.981625, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 37.54, "width_percent": 2.863}, {"sql": "select `ec_products`.*, `ec_flash_sale_products`.`flash_sale_id` as `pivot_flash_sale_id`, `ec_flash_sale_products`.`product_id` as `pivot_product_id`, `ec_flash_sale_products`.`price` as `pivot_price`, `ec_flash_sale_products`.`quantity` as `pivot_quantity`, `ec_flash_sale_products`.`sold` as `pivot_sold` from `ec_products` inner join `ec_flash_sale_products` on `ec_products`.`id` = `ec_flash_sale_products`.`product_id` where `ec_flash_sale_products`.`flash_sale_id` in (1) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 25}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": 1764436117.9843738, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 40.403, "width_percent": 3.393}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-11-29 17:08:37' and (`end_date` is null or `end_date` >= '2025-11-29 17:08:37') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-11-29 17:08:37", "2025-11-29 17:08:37", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 116}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": 1764436117.9943151, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 43.796, "width_percent": 6.716}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436118.004394, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 50.513, "width_percent": 4.843}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 7 and not `parent_id` = 9 limit 1", "type": "query", "params": [], "bindings": [7, 9], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436118.008492, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 55.355, "width_percent": 3.358}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 2 and not `parent_id` = 7 limit 1", "type": "query", "params": [], "bindings": [2, 7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 122}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436118.01178, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 58.713, "width_percent": 1.979}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 2 limit 1", "type": "query", "params": [], "bindings": [0, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 122}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436118.013877, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 60.693, "width_percent": 1.202}, {"sql": "select * from `ec_brands` where `ec_brands`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1450}, {"index": 25, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436118.016752, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 61.895, "width_percent": 3.429}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": "view", "name": "plugins/ecommerce::themes.includes.product-price", "file": "D:\\hassan code\\shofy\\platform/plugins/ecommerce/resources/views/themes/includes/product-price.blade.php", "line": 13}], "start": 1764436118.035859, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 65.323, "width_percent": 4.666}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 88}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436118.055589, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 69.989, "width_percent": 2.651}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 32 limit 1", "type": "query", "params": [], "bindings": [0, 32], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436118.059132, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 72.641, "width_percent": 2.969}, {"sql": "select * from `ec_brands` where `ec_brands`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1450}, {"index": 25, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 88}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436118.061685, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 75.61, "width_percent": 1.555}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 3", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436118.090338, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 77.165, "width_percent": 2.651}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 32 and not `parent_id` = 36 limit 1", "type": "query", "params": [], "bindings": [32, 36], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436118.0935159, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 79.816, "width_percent": 2.651}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 32 limit 1", "type": "query", "params": [], "bindings": [0, 32], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 122}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436118.096127, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 82.467, "width_percent": 1.237}, {"sql": "select * from `ec_brands` where `ec_brands`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1450}, {"index": 25, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436118.098499, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 83.704, "width_percent": 2.828}, {"sql": "select `ec_product_categories`.*, `ec_product_category_product`.`product_id` as `pivot_product_id`, `ec_product_category_product`.`category_id` as `pivot_category_id` from `ec_product_categories` inner join `ec_product_category_product` on `ec_product_categories`.`id` = `ec_product_category_product`.`category_id` where `ec_product_category_product`.`product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1434}, {"index": 23, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1764436118.13006, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "shofy", "explain": null, "start_percent": 86.532, "width_percent": 3.782}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` = 0 and not `parent_id` = 32 limit 1", "type": "query", "params": [], "bindings": [0, 32], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/ProductCategory.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Models\\ProductCategory.php", "line": 118}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1439}], "start": 1764436118.134201, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 90.315, "width_percent": 5.444}, {"sql": "select * from `ec_brands` where `ec_brands`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\hassan code\\shofy\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 1450}, {"index": 25, "namespace": "view", "name": "theme.shofy::views.ecommerce.includes.product.style-1.actions", "file": "D:\\hassan code\\shofy\\platform\\themes/shofy/views/ecommerce/includes/product/style-1/actions.blade.php", "line": 81}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\hassan code\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1764436118.139401, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\hassan code\\shofy\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "shofy", "explain": null, "start_percent": 95.758, "width_percent": 4.242}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\ProductCategory": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCategory.php&line=1", "ajax": false, "filename": "ProductCategory.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Product": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductCollection": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCollection.php&line=1", "ajax": false, "filename": "ProductCollection.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Brand": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductVariation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1", "ajax": false, "filename": "ProductVariation.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\FlashSale": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FFlashSale.php&line=1", "ajax": false, "filename": "FlashSale.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductLabel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fhassan%20code%2Fshofy%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductLabel.php&line=1", "ajax": false, "filename": "ProductLabel.php", "line": "?"}}}, "count": 61, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "language": "en"}, "request": {"telescope": "<a href=\"http://127.0.0.1:8000/_debugbar/telescope/a0797ace-293d-4c86-9c2b-bfac2c124c16\" target=\"_blank\">View in Telescope</a>", "path_info": "/ajax/render-ui-blocks", "status_code": "<pre class=sf-dump id=sf-dump-1511313591 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1511313591\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1418406724 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1418406724\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1136309929 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">ecommerce-flash-sale</span>\"\n  \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>style</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Deal of The Day</span>\"\n    \"<span class=sf-dump-key>flash_sale_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str>4</span>\"\n    \"<span class=sf-dump-key>button_label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">View All Deals</span>\"\n    \"<span class=sf-dump-key>button_url</span>\" => \"<span class=sf-dump-str title=\"9 characters\">/products</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136309929\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">226</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;142&quot;, &quot;Google Chrome&quot;;v=&quot;142&quot;, &quot;Not_A Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/142.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">en-GB,en-US;q=0.9,en;q=0.8,ar;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1957 characters\">botble_footprints_cookie=eyJpdiI6ImRFeFg2U3EwYUtoR05hRi9RVWdmN0E9PSIsInZhbHVlIjoiVk55ay9yby9GSzFqbEIzV2tzLzJtQis3OHJPWURuN014SmtndlQ4ZmhwN0JSNWdQMmxXVG9abmdrVGIwcENEdDVxc2tvWWlMK0s1WlBXa29wK1IzZUZWeHV6aGE0MTB3UU1YYy9OK0JYcXgvdUw3WGxVVGVWbVVkcStwbFBIWWEiLCJtYWMiOiJlMmI4MjIyMWM3NGI5OWUxYTU3M2RiMDlkNmIxZDE5ODE3NmIxNDJhNzJjODFiYjllYTk2NjY4NDUzMWZhNGUwIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6IkZ6Z1d1ZjZtRlhZdy80MFJxOER2T0E9PSIsInZhbHVlIjoibDhQbEMyOStBTlQ5czkzYWRIMGczRDhRMEFVMWNYOVQ1eU0zZ1hYVlp2VWhGUVZmYlYxTksrS0I4aWI3b0FMTXRDd2sybTRGSFZwRGEzN0JTQzZMWFF0S0wwMndURVhIRm51UFQvVElkSFR6dmlBWjRKMVROamc4dW9HTGJselFDTlZQNC9qeDdNOXJvd0djRmN1anc3UHdSL2p1cE9RZGxROEc0SE15elhQUFJ4Q2Y3ODhycmVoM1EwNUJaQTRCY0VXVzNiUmNnS21GM1hWMnJwYUNXWHlIUFd6cmlYRlBnNnMwY3lpK0MzR21lUjRtSm1NZHRPbmUvR2szQngwdW4wZUhVM1Z4NC9PVGxJdG9pVWdPK2RZTXF4N1lUWjR3ZndTeGVFKzZVTmE0RHI4cWp2akJJaEo4WmZWc0s1Y0kwdWx1TGNEeW45ZEJzbDZNeWtadXJKMi9OOHJTcUZrVEIrRTBRa1RNZjFNUVJyaHpWYzdObHpjNU5zMUtvVlVQUlN6amt1aEcwR25iTWhSdFpYcG1jZGFmS0QyN01JVTdpcmtBeDVSQ1BpaVFxbjhBMVZtNEo2eGxGL2lpcjJiT1JreWdFQ3FkcFZraDg3MUNqU253ZmJGck9aSGc0NzNOVWpOUjBoNzU1VFE9IiwibWFjIjoiMTE0ZjdmM2EzODVjMWUxYzFmYzgwNmZhNWEwYzZjYTQwOTc2ODVkNTk1NDZkZGE0MzAwODc5OGMyNzg4ODc2ZCIsInRhZyI6IiJ9; newsletter_popup=1; XSRF-TOKEN=eyJpdiI6IjVEYVhsbzN1YllwcUFTaXVObWtSZkE9PSIsInZhbHVlIjoiT01XQ1Z5WHZzTjRSd0RUd0V0WkxCdDJiWE9zT3N5RityMkcxcCsyUnljellUNjZXSmZuWGZZS0E1RkVMRDFuWS96Nlp0cTZjNjFnSzlycFNxREgzb25ZOHRYWFZtVHk5bGs0czg2em1GRFFwUU9tM0xLWG92dzl4MnpmMmg4RzMiLCJtYWMiOiIxZDMyZjc0YjZlYzhiMjQzNDJmYWY5ZjVjMWQ5YmQ1YTM2MGQ4ZDg0MjUyZGQ3NWVlYjQ0YmNkYzI2YTlkNmFjIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InZ3ZVI5SmpJN3hzdXJhNmJCUUd6R2c9PSIsInZhbHVlIjoiYUVaYVpjdjNKSnJJclRVSUtrVEFWUFEyTjlHa1pUb3pkQklGK1RYSWZhcjltN004MjhxSHFxTTR0QlAwb001Vy84YVYvaUxreUFDVklMQXBRSHR3ZW84V2s4Ry9nL1FLM3lENGNwaFhveUExZ1FjMk5oOFpnVnAzS20wTUNML0oiLCJtYWMiOiJhN2JjNzY2NGYyMGNiYWU4Y2I2NGQ2YjY0ODg0Y2U1ODMwYTMyZDMyYjRmZTljYThjN2VkMGYzNDc1MGFjYWY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5832e034bf28c6f97be3a608bb8c655f40381a49</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"320 characters\">{&quot;footprint&quot;:&quot;5832e034bf28c6f97be3a608bb8c655f40381a49&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;127.0.0.1&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:null,&quot;referrer_domain&quot;:null}</span>\"\n  \"<span class=sf-dump-key>newsletter_popup</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PMPD3sRy7YrBqKNvMw96LkySo4AQ8DJuqi1a8JCS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1808323406 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 29 Nov 2025 17:08:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii9oT2hoUmdnTDFMeWtDTlZwc1ltU0E9PSIsInZhbHVlIjoiT3p3TkJiRUpoV1M1UUtCbEcvb1Y0MFY5VkI5VVJvVlFOV1FLOEJEYzl5aXE4citLazYvUGZSNStES0RtVWF4bVZaNlppSmJlcVZiYVVicWs5TW9PZTFyVnltMCtNWFVjL2Q5Wm56OGRuZExvaUlvcG1jMGtKakhMcHNlOThuc3EiLCJtYWMiOiJkYzQwZGQwZDAzNDYwNWUxZjUzYWJhMGJjNDdkOWZjMjBiN2VhOTljMGRkZDc3ZTkyY2IyZGQ3MjA1NzNhMGNlIiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:08:38 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"442 characters\">botble_session=eyJpdiI6ImIwaFVXQXhRenE3ZTE3SHpCWWFxc3c9PSIsInZhbHVlIjoiOS9LQmFmNmtKVUh1NW9Nbk5TbTZXeVFGN0VQSUUwbllXejZXNkRHTU1Cd3VpbUJiZ0lIQ3VsOHFHV0QveGRneG9nckZ2dDN5Y0t6MjlvNXhubEZDNkk4T1dmZUZPcS8zTmFWems5azl0VWNUcHFOWDRaa2N1MW1RbzdDK2tkQnAiLCJtYWMiOiIyYzI4ZjM5NTFiZmY2MGEzNTUyNmRlMDQ3ZjNkODFkZWM5M2ZjYTZlNzVlZjE1NDMxMGM0NjgzZWI2MTEyYWQ2IiwidGFnIjoiIn0%3D; expires=Sat, 29 Nov 2025 19:08:38 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii9oT2hoUmdnTDFMeWtDTlZwc1ltU0E9PSIsInZhbHVlIjoiT3p3TkJiRUpoV1M1UUtCbEcvb1Y0MFY5VkI5VVJvVlFOV1FLOEJEYzl5aXE4citLazYvUGZSNStES0RtVWF4bVZaNlppSmJlcVZiYVVicWs5TW9PZTFyVnltMCtNWFVjL2Q5Wm56OGRuZExvaUlvcG1jMGtKakhMcHNlOThuc3EiLCJtYWMiOiJkYzQwZGQwZDAzNDYwNWUxZjUzYWJhMGJjNDdkOWZjMjBiN2VhOTljMGRkZDc3ZTkyY2IyZGQ3MjA1NzNhMGNlIiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:08:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"414 characters\">botble_session=eyJpdiI6ImIwaFVXQXhRenE3ZTE3SHpCWWFxc3c9PSIsInZhbHVlIjoiOS9LQmFmNmtKVUh1NW9Nbk5TbTZXeVFGN0VQSUUwbllXejZXNkRHTU1Cd3VpbUJiZ0lIQ3VsOHFHV0QveGRneG9nckZ2dDN5Y0t6MjlvNXhubEZDNkk4T1dmZUZPcS8zTmFWems5azl0VWNUcHFOWDRaa2N1MW1RbzdDK2tkQnAiLCJtYWMiOiIyYzI4ZjM5NTFiZmY2MGEzNTUyNmRlMDQ3ZjNkODFkZWM5M2ZjYTZlNzVlZjE1NDMxMGM0NjgzZWI2MTEyYWQ2IiwidGFnIjoiIn0%3D; expires=Sat, 29-Nov-2025 19:08:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1808323406\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-336241765 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zZ8VYCLmGg5hMjczM4GlxaheD3RGkh5YVm0YKqvb</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-336241765\", {\"maxDepth\":0})</script>\n"}}