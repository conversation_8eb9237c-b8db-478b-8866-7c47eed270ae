<section class="tp-product-category pt-60 pb-15">
    <div class="container">
        <?php echo Theme::partial('section-title', compact('shortcode')); ?>

        <div class="tp-product-categories-slider swiper-container" data-items="<?php echo e((int) $shortcode->items_per_view ?: 5); ?>">
            <div class="swiper-wrapper">
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="swiper-slide">
                        <div class="tp-product-category-item text-center mb-40">
                            <div class="tp-product-category-thumb fix">
                                <a href="<?php echo e($category->url); ?>" title="<?php echo e($category->name); ?>">
                                    <?php echo e(RvMedia::image($category->image, $category->name)); ?>

                                </a>
                            </div>
                            <div class="tp-product-category-content">
                                <h3 class="tp-product-category-title">
                                    <a href="<?php echo e($category->url); ?>" title="<?php echo e($category->name); ?>"><?php echo e($category->name); ?></a>
                                </h3>
                                <?php if($shortcode->show_products_count): ?>
                                    <p>
                                        <?php if($category->products_count === 1): ?>
                                            <?php echo e(__('1 product')); ?>

                                        <?php else: ?>
                                            <?php echo e(__(':count products', ['count' => number_format($category->products_count)])); ?>

                                        <?php endif; ?>
                                    </p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</section>
<?php /**PATH D:\hassan code\shofy\platform\themes/shofy/partials/shortcodes/ecommerce-categories/slider.blade.php ENDPATH**/ ?>