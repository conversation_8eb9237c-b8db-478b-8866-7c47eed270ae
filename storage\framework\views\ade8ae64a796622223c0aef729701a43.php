<section class="tp-slider-area p-relative z-index-1">
    <div
        class="tp-slider-active tp-slider-variation swiper-container"
        data-loop="<?php echo e($shortcode->is_loop == 'yes'); ?>"
        data-autoplay="<?php echo e($shortcode->is_autoplay == 'yes'); ?>"
        data-autoplay-speed="<?php echo e(in_array($shortcode->autoplay_speed, [2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000]) ? $shortcode->autoplay_speed : 5000); ?>"
    >
        <div class="swiper-wrapper">
            <?php $__currentLoopData = $sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slider): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $title = $slider->title;
                    $subtitle = $slider->getMetaData('subtitle', true);
                    $description = $slider->description;
                ?>

                <div
                    class="<?php echo \Illuminate\Support\Arr::toCssClasses(['tp-slider-item tp-slider-height d-flex align-items-center swiper-slide', 'is-light' => $slider->getMetaData('is_light', true)]); ?>"
                    style="background-color: <?php echo e($slider->getMetaData('background_color', true)); ?>"
                >
                    <div class="tp-slider-shape d-none d-sm-block">
                        <?php $__currentLoopData = range(1, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($shape = $shortcode->{"shape_$i"}): ?>
                                <?php echo e(RvMedia::image($shape, $slider->title, attributes: ['class' => "tp-slider-shape-$i", 'loading' => 'lazy'])); ?>

                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="container">
                        <?php if($title || $description): ?>
                            <div class="row align-items-center">
                                <div class="col-xl-5 col-lg-6 col-md-6">
                                    <div class="tp-slider-content p-relative z-index-1">
                                        <?php if($subtitle): ?>
                                            <span><?php echo BaseHelper::clean($subtitle); ?></span>
                                        <?php endif; ?>
                                        <?php if($title): ?>
                                            <h3 class="tp-slider-title" style="<?php echo \Illuminate\Support\Arr::toCssStyles(["font-size: {$shortcode->title_font_size}px" => $shortcode->title_font_size]) ?>"><?php echo BaseHelper::clean($title); ?></h3>
                                        <?php endif; ?>
                                        <?php if($description): ?>
                                            <p <?php if($fontFamily = $shortcode->font_family_of_description): ?> style="--tp-ff-oregano: '<?php echo e($fontFamily); ?>'" <?php endif; ?>>
                                                <?php echo BaseHelper::clean($description); ?>

                                            </p>
                                        <?php endif; ?>
                                        <?php if($buttonLabel = $slider->getMetaData('button_label', true)): ?>
                                            <div class="tp-slider-btn">
                                                <a href="<?php echo e($slider->link); ?>" class="tp-btn tp-btn-2 tp-btn-white">
                                                    <?php echo BaseHelper::clean($buttonLabel); ?>

                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-xl-7 col-lg-6 col-md-6">
                                    <div class="tp-slider-thumb text-end">
                                        <?php echo $__env->make(Theme::getThemeNamespace('partials.shortcodes.simple-slider.includes.image', ['slider' => $slider, $loop->index ? ['loading' => 'lazy'] : ['loading' => 'eager']]), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="tp-slider-thumb text-end">
                                <?php echo $__env->make(Theme::getThemeNamespace('partials.shortcodes.simple-slider.includes.image', ['slider' => $slider, $loop->index ? ['loading' => 'lazy'] : ['loading' => 'eager']]), \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <?php if(count($sliders) > 1): ?>
            <div class="tp-slider-arrow tp-swiper-arrow d-none d-lg-block">
                <button type="button" class="tp-slider-button-prev">
                    <svg width="8" height="14" viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M7 13L1 7L7 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
                <button type="button" class="tp-slider-button-next">
                    <svg width="8" height="14" viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 13L7 7L1 1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            <div class="tp-slider-dot tp-swiper-dot"></div>
        <?php endif; ?>
    </div>
</section>
<?php /**PATH D:\hassan code\shofy\platform\themes/shofy/partials/shortcodes/simple-slider/style-1.blade.php ENDPATH**/ ?>