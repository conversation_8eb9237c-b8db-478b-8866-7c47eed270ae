<?php

namespace App\Console\Commands;

use Botble\Blog\Models\Category;
use Botble\Blog\Models\Tag;
use <PERSON><PERSON>ble\Ecommerce\Enums\ProductTypeEnum;
use Botble\Ecommerce\Models\Product;
use Botble\Ecommerce\Models\ProductAttribute;
use Botble\Ecommerce\Models\ProductAttributeSet;
use Botble\Ecommerce\Models\ProductVariation;
use Botble\Ecommerce\Models\ProductVariationItem;
use Botble\Media\Facades\RvMedia;
use Botble\Media\Models\MediaFile;
use Botble\Media\Models\MediaFolder;
use Botble\Slug\Models\Slug;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Models\ScrapShopify;

class ImportShopifyProducts extends Command
{
    protected $signature = 'import:shopify-products 
                           {--url= : Shopify store URL}
                           {--per-page=250 : Items per page}
                           {--max-pages= : Maximum pages to import}
                           {--current-page=1 : Current page to start from}';
    protected string $baseUrl;                           
    
    protected $description = 'Import products from a Shopify store via API';

    public function handle()
    {
        ini_set('max_execution_time', 0);
        set_time_limit(0);
        $shopifyStoreUrl = $this->option('url') ?? 'https://jo-cell.com';
        $this->baseUrl = $shopifyStoreUrl;    
        $perPage = (int)$this->option('per-page');
        $maxPages = $this->option('max-pages');
        $currentPage = $this->option('current-page', 1);
        $round=0;    
        $importedCount = 0;
        
        $scrapShopifyModel = ScrapShopify::where('site',$shopifyStoreUrl)->first();
   
        if(is_null($scrapShopifyModel)){
           $scrapShopifyModel = ScrapShopify::create([ 'site'=>$shopifyStoreUrl,'pageNumber'=> $currentPage]);
        }else{
         $currentPage = (int) ScrapShopify::where('site',$shopifyStoreUrl)->orderBy('pageNumber','DESC')->first()->pageNumber;
          ScrapShopify::where('site',$shopifyStoreUrl)->delete(); 
          $currentPage ++;
          $scrapShopifyModel = ScrapShopify::create([ 'site'=>$shopifyStoreUrl,'pageNumber'=> $currentPage]);
        }
        
        $this->info("Starting Shopify product import from: {$shopifyStoreUrl}");

        do {
            try {
                $response = $this->fetchProductsPage($shopifyStoreUrl, $perPage, $currentPage);
                
                if ($response->failed()) {
                    $this->error("Failed to fetch page {$currentPage}: HTTP {$response->status()}");
                    break;
                }

                $data = $response->json();
                $round ++;
                if (empty($data['products'])) {
                    $this->info("No more products found on page {$currentPage}");
                    break;
                }

                $this->info("Processing page {$currentPage} with ".count($data['products'])." products");
                
                DB::transaction(function() use ($data, &$importedCount) {
                    foreach ($data['products'] as $product) {
                        $this->processProduct($product);
                        $importedCount++;
                    }
                });

                $this->info("Successfully processed page {$currentPage}");
                
            } catch (\Exception $e) {
                $this->error("Error processing page {$currentPage}: ".$e->getMessage());
                break;
            }
            
            $currentPage++;
            
        } while (
            count($data['products']) === $perPage && 
            (!$maxPages || $currentPage <= $maxPages)   &&  $round >= 5 
        );
        
        $scrapShopifyModel = ScrapShopify::create([ 'site'=>$shopifyStoreUrl,'pageNumber'=> $currentPage]);
        
        $this->info("Import completed. Total products imported: {$importedCount}");
        return 0;
    }

    protected function fetchProductsPage(string $shopifyUrl, int $perPage, int $page)
    {
        $apiUrl = "{$shopifyUrl}/products.json?limit={$perPage}&page={$page}";
        $this->info("Fetching products from: {$apiUrl}");
        return Http::withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
            'Cookie' => 'shopify_test_cookie=1;',
        ])
        ->timeout(30)
        ->retry(3, 1000)
        ->get($apiUrl);
    }

    protected function processProduct(array $productData)
    {
        // Create or update main product
        $mainProduct = $this->createMainProduct($productData);
        
        // Process product attributes and variations
        if (!empty($productData['options'])) {
            $this->processProductAttributes($productData, $mainProduct);
        }
        
        // Process variants if they exist
        if (!empty($productData['variants'])) {
            $this->processProductVariants($productData, $mainProduct);
        }

        $this->processProductImages($productData ,$mainProduct);

        
        $this->asignImageToVariations($productData);
        $this->processTags($productData, $mainProduct);


           //$this->processVendor($productData, $mainProduct);

        $this->processProductType($productData, $mainProduct); //producttType 

    
    }

    protected function isArabicText(string $text): bool
    {
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text) === 1;
    }

      protected function fetchProductByHandle(string $handle, string $locale = 'ar')
    {

        if ($locale == 'ar') {
            $apiUrl = "{$this->baseUrl}/{$locale}/products/{$handle}.json";
        } else {
            $apiUrl = "{$this->baseUrl}/products/{$handle}.json";
        }
        $this->info("Fetching {$locale} version for {$handle} from: {$apiUrl}");

        try {
            $response = Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                'Cookie' => 'shopify_test_cookie=1;',
            ])
                ->timeout(30)
                ->retry(3, 1000)
                ->get($apiUrl);

            if ($response->successful()) {
                return $response->json('product');
            }
        } catch (Exception $e) {
            $apiUrl = "{$this->baseUrl}/products/{$handle}.json";
            $response = Http::withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                'Cookie' => 'shopify_test_cookie=1;',
            ])
                ->timeout(30)
                ->retry(3, 1000)
                ->get($apiUrl);

            if ($response->successful()) {
                return $response->json('product');
            }

            $this->warn("Failed to fetch {$locale} version for {$this->baseUrl}/products/{$handle}: {$e->getMessage()}");
        }

        return null;
    }
    protected function createMainProduct(array $productData): Product
    {

        $isArabic = $this->isArabicText($productData['title']);
        $arabicData = null;
        $englishData = null;

        if ($isArabic) {
            $arabicData = $productData;
            $englishData = $this->fetchProductByHandle($productData['handle'], 'en');
        } else {
            $englishData = $productData;
            $arabicData = $this->fetchProductByHandle($productData['handle'], 'ar');
        }

         $Product= Product::updateOrCreate(
            ['shopify_id' => $arabicData['id']],
            [
                'name' => $arabicData['title'],
                'description' => $arabicData['body_html'] ?? '',
                'content' => $arabicData['body_html'] ?? '',
                'status' => $arabicData['status'] ?? 'published',
                'is_featured' => $arabicData['is_featured'] ?? false,
                'price' => $this->parsePrice($arabicData['variants'][0]['price'] ?? 0),
                // 'sale_price' => $this->parsePrice($productData['variants'][0]['compare_at_price'] ?? null),
                'sku' => $arabicData['variants'][0]['sku'] ?? null,
                'barcode' => $arabicData['variants'][0]['barcode'] ?? null,
                'quantity' => $arabicData['variants'][0]['inventory_quantity'] ?? 0,
                'weight' => $arabicData['variants'][0]['weight'] ?? null,
                'product_type' => ProductTypeEnum::PHYSICAL,
                'is_variation' => 0,
                'with_storehouse_management' => false,
                'stock_status' => 'in_stock',
                // 'stock_status' => ($productData['variants'][0]['inventory_quantity'] ?? 0) > 0 
                //     ? 'in_stock' 
                //     : 'out_of_stock',
            ]
        );
        $this->info("Product created: {$Product->name}");
        if ($englishData && $englishData['title']) {
            DB::table('ec_products_translations')->updateOrInsert(
                [
                    'lang_code' => 'en_US',
                    'ec_products_id' => $Product->id,
                ],
                [
                    'name' => $englishData['title'],
                    'description' => $englishData['body_html'] ?? '',
                    'content' => $englishData['body_html'] ?? '',
                ]
            );
        }

        
        $slug = Slug::updateOrCreate([
            'reference_id' => $Product->id,
            'reference_type' => Product::class,
        ],[
            'key' => Str::slug($Product->name),
            'prefix' => 'products',
            'reference_id' => $Product->id,
            'reference_type' => Product::class,

        ]);
        return $Product;
    }


    
    protected function processProductAttributes(array $productData, Product $mainProduct)
    {
        foreach ($productData['options'] as $option) {
            $attributeSet = ProductAttributeSet::updateOrCreate(
                ['title' => $option['name']],
                [
                    'title' => $option['name'],
                    'slug' => Str::slug($option['name']),
                    'status' => 'published',
                    'order' => 0,
                    'display_layout' => "text",
                    'is_searchable' => true,
                    'is_comparable' => true,
                    'is_use_in_product_listing' => true,
                    'use_image_from_product_variation' => true,
                ]
            );
            
            // Attach attribute set to product
            $mainProduct->productAttributeSets()->syncWithoutDetaching([$attributeSet->id]);
            
            // Create attributes for each option value
            foreach ($option['values'] as $value) {
               $attribute= ProductAttribute::updateOrCreate(
                    [
                        'title' => $value,
                        'attribute_set_id' => $attributeSet->id,
                    ],
                    [
                        'title' => $value,
                        'slug' => Str::slug($value),
                        'attribute_set_id' => $attributeSet->id,
                        'order' => 0,
                    ]
                );
            
            }
        }
    }


  protected function processProductVariants(array $productData, Product $mainProduct)
    {
        $variants = [];


        // // Create product variations if we have multiple variants
       ProductVariation::where('configurable_product_id', $mainProduct->id)->delete();

        if (count($productData['variants']) > 1) {
            foreach ($productData['variants'] as $index => $variant) {


                $variantProduct = $this->createVariantProduct($productData, $variant, $mainProduct);

                $variantModel = ProductVariation::create([
                    'configurable_product_id' => $mainProduct->id,
                    'product_id' => $variantProduct->id,
                    'is_default' => $index === 0,
                ]);
                

                $this->processVariantAttributes($mainProduct, $variantProduct, $variantModel, $variant);
            }

         

            $this->info("Product variations created for {$mainProduct->name}");
        }
    }





     protected function createVariantProduct(array $productData, array $variant, Product $mainProduct): Product
    {
        
        $Product = Product::updateOrCreate(
            ['shopify_id' => $variant['id']],
            [
                'name' => $productData['title'],
                'description' => '',
                'status' => 'published',
                'price' => $this->parsePrice($variant['price'] ?? 0),
                // 'sale_price' => $this->parsePrice($variant['compare_at_price'] ?? null),
                'sku' => $variant['sku'] ?? null,
                'barcode' => $variant['barcode'] ?? null,
                'quantity' => $variant['inventory_quantity'] ?? 0,
                'weight' => $variant['weight'] ?? null,
                'product_type' => ProductTypeEnum::PHYSICAL,
                'is_variation' => true,
                'with_storehouse_management' => false,
                'stock_status' => 'in_stock',
                // 'stock_status' => ($variant['inventory_quantity'] ?? 0) > 0
                //     ? 'in_stock'
                //     : 'out_of_stock',
            ]
        );

        $slug = Slug::updateOrCreate([
            'reference_id' => $Product->id,
            'reference_type' => Product::class,
        ], [
            'key' => Str::slug($Product->name),
            'prefix' => 'products',
            'reference_id' => $Product->id,
            'reference_type' => Product::class,

        ]);


        return $Product;
    }

      protected function processVariantAttributes($mainProduct, $variantProduct, $variantModel ,$variant)
    {
        $attributesToAttach = [];

        // Process each variant option
        for ($i = 1; $i <= 3; $i++) {
            $optionKey = "option{$i}";

            if (empty($variant[$optionKey])) {
                continue;
            }

            $optionValue = $variant[$optionKey];
       
            // Find matching attribute
            $attribute = ProductAttribute::where('title', $optionValue)->first();

            if (!$attribute) {

                  $attribute = ProductAttribute::create([
                    'title' => $optionValue,
                    'attribute_set_id' => $mainProduct->attribute_set_id,
                ]);
              
            } 
            ProductVariationItem::updateOrCreate(
                [
                    'variation_id' => $variantModel->id,
                    'attribute_id' => $attribute->id,
                ],
                [
                    'variation_id' => $variantModel->id,
                    'attribute_id' => $attribute->id,
                ]
            );
        }
     
    }


    protected function createProductVariations(Product $mainProduct, array $variantIds)
    {
        // First delete any existing variations
        ProductVariation::where('configurable_product_id', $mainProduct->id)->delete();
        
        // Create new variations
        foreach ($variantIds as $index => $variantId) {
            ProductVariation::create([
                'configurable_product_id' => $mainProduct->id,
                'product_id' => $variantId,
                'is_default' => $index === 0,
            ]);
        }
    }


protected function processProductImages(array $productData, Product $product)
{
    if (empty($productData['images'])) {
        return;
    }

    $imageUrls = [];
    $folderId = $this->ensureProductImagesFolder($product);

    foreach ($productData['images'] as $image) {
        try {
            $mediaModel = MediaFile::where('shopify_id', $image['id'])->first();
            
            if (is_null($mediaModel)) {
                $imageUrl = $image['src'];
                $imageName = $this->generateImageName($imageUrl);
                $uploadResult = $this->downloadAndStoreImage($imageUrl, $folderId, $imageName);
                
                if ($uploadResult && $uploadResult['error'] === false) {
                    $fileData = $uploadResult['data'];
                    $imageUrls[] = $fileData['url'];

                        $mediaModel = MediaFile::create([
                            'name' => $fileData['name'],
                            'mime_type' => $fileData['mime_type'],
                            'size' => $fileData['size'],
                            'url' => $fileData['url'],
                            'folder_id' => $folderId,
                            'shopify_id' =>(string) $image['id'],
                            'user_id' => 0,
                        ]);
                    

                       if (!$mediaModel) {
                            $this->error("Failed to insert MediaFile record");
                        } 
                }
            } else {
                $imageUrls[] = $mediaModel->url;
            }
        } catch (Exception $e) {
            $this->error("Failed to process image: {$e->getMessage()}");
            continue;
        }
    }

    if (!empty($imageUrls)) {
        $product->update([
            'images' => $imageUrls,
            'image' => $imageUrls[0] ?? null
        ]);
    }
}
    protected function ensureProductImagesFolder(Product $product): int
    {
        // $folder = MediaFolder::firstOrCreate(
        //     ['name' => "$product->id"],
        //     [
        //         'parent_id' => 0,
        //         'slug' => 'shopify-products',
        //         'user_id' => 0,
        //     ]
        // );

        $folder = MediaFolder::updateOrCreate(
            ['name' => "$product->id"],
            [
            'name' => "$product->id",
               'parent_id' => 10,
                'slug' => 'shopify-products',
                'user_id' => 0,
            ]
        );

        return $folder->id;
    }

protected function downloadAndStoreImage(string $imageUrl, int $folderId, string $imageName): ?array
{
    try {
        // Set a strict timeout (30 seconds as per your requirement)
        set_time_limit(30);
        
        // Create tmp directory if it doesn't exist
        $tmpDir = storage_path('app/public/tmp');
        if (!file_exists($tmpDir)) {
            mkdir($tmpDir, 0755, true);
        }

        // Download the image with strict timeout
        $response = Http::timeout(20) // Lower than max execution time
            ->connectTimeout(10)
            ->retry(3, 1000)
            ->get($imageUrl);

        if (!$response->successful()) {
            $this->warn("Failed to download image: {$imageUrl} - HTTP {$response->status()}");
            return null;
        }

        $imageData = $response->getBody()->getContents();

        // Clean the URL to get proper extension
        $cleanedUrl = preg_replace('/\?.*/', '', $imageUrl);
        $fileExtension = pathinfo($cleanedUrl, PATHINFO_EXTENSION) ?: 'jpg';
        $fileName = $imageName . '.' . $fileExtension;

        // Store in temporary location
        $storagePath = $tmpDir . '/' . $fileName;
        file_put_contents($storagePath, $imageData);

        // Import to media library with timeout protection
        try {
            $uploadResult = RvMedia::uploadFromPath(
                $storagePath,
                $folderId,
                'products',
                $fileName
            );
        } catch (\Exception $e) {
            $this->warn("Media library processing failed for {$imageUrl}: {$e->getMessage()}");
            return null;
        } finally {
            // Clean up temp file
            if (file_exists($storagePath)) {
                unlink($storagePath);
            }
        }

        return $uploadResult;
    } catch (\Exception $e) {
        $this->warn("Image download failed: {$imageUrl} - {$e->getMessage()}");
        return null;
    }
}
    protected function generateImageName(string $imageUrl): string
    {
        $baseName = pathinfo($imageUrl, PATHINFO_FILENAME);
        return Str::slug($baseName) . '-' . Str::random(5);
    }

    protected function parsePrice($price)
    {
        if (is_null($price)) {
            return null;
        }
        
        return (float) preg_replace('/[^0-9.]/', '', $price);
    }



     public function asignImageToVariations(array $productData)
    {
        if (empty($productData['variants'])) {
            return;
        }

        foreach ($productData['variants'] as $variant) {
            $variantProduct = Product::where('shopify_id', $variant['id'])->first();

            if (!$variantProduct) {
                continue;
            }

           if(isset($variant['image_id'])){
              $media = MediaFile::where('shopify_id', $variant['image_id'])->first();
              if($media) {
                    $variantProduct->update(['image' => $media->url]);
                } 
           }
        }
    }


       public function processTags(array $productData, Product $product)
    {
        if (isset($productData['tags'])) {
              if (!is_array($productData['tags'])) {
                    $tags = explode(',', $productData['tags']);
                } else {
                    $tags = array_map('trim', $productData['tags']);
                }
                $tagsId=[];
                foreach ($tags as $key => $tag) {
                    $tag = trim($tag);
                    $tagsId[] = Tag::updateOrCreate(
                        ['name' => $tag],
                        [
                            'name' => $tag,
                            'description' => '',
                            'status' => 'published',
                            'author_id' => 0,
                            'author_type' => 'system',
                        ]
                    )->id;
                }
                try {
                    $product->tags()->sync($tagsId);
                } catch (Exception $e) {
                    $this->error("Failed to detach existing tags: " . $e->getMessage());
                }
         
                return $tags;
        }
    }



        public function processVendor(array $productData, Product $product)
    {
        if (isset($productData['vendor'])) {
            $vendor = trim($productData['vendor']);
            if (!empty($vendor)) {
                $product->update(['vendor' => $vendor]);
            } else {
                $this->warn("Vendor is empty for product: {$product->name}");
            }
        } else {
            $this->warn("No vendor found for product: {$product->name}");
        }
    }




    public function processProductType(array $productData, Product $product)
    {
        if (isset($productData['product_type'])) {
            $productType = trim($productData['product_type']);
            if (!empty($productType)) {
               
                $category = Category::updateOrCreate(
                    ['name' => $productType],
                    [
                        'name' => $productType,
                        'description' => '',
                        'status' => 'published',
                        'is_default' => false,
                    ]
                );
                $product->categories()->syncWithoutDetaching([$category->id]);

            } else {
                $this->warn("Product type is empty for product: {$product->name}");
            }
        } else {
            $this->warn("No product type found for product: {$product->name}");
        }
    }
}