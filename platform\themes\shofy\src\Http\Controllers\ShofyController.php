<?php

namespace Theme\Shofy\Http\Controllers;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Ecommerce\Models\Product;
use Botble\Ecommerce\Services\Products\GetProductService;
use Botble\Ecommerce\Services\Products\ProductCrossSalePriceService;
use Botble\Ecommerce\Supports\EcommerceHelper;
use Botble\Support\Services\Cache\Cache;
use Botble\Theme\Facades\Theme;
use Botble\Theme\Http\Controllers\PublicController;
use Carbon\Carbon;
use Illuminate\Http\Request;

class ShofyController extends PublicController
{
    public function ajaxGetProducts(Request $request, GetProductService $productService): BaseHttpResponse
    {
        $limit = $request->integer('limit', 10);
        $type = $request->query('type', 'all');
        $query = $request->input('q');

        // Create cache key based on request parameters
        $cache = Cache::make(Product::class);
        $cacheKey = 'ajax_products_' . md5(serialize([
            'limit' => $limit,
            'type' => $type,
            'query' => $query,
            'layout' => 'grid',
            'items_per_row' => get_products_per_row(),
        ]));

        // Check if this is a search request
        if ($request->has('q') && $request->input('q')) {
            // For search requests, use shorter cache time due to dynamic nature
            return $cache->remember($cacheKey, Carbon::now()->addMinutes(15), function () use ($request, $productService, $limit, $query) {
                // Use GetProductService for search functionality
                $request->merge(['num' => $limit]);

                $with = app(EcommerceHelper::class)->withProductEagerLoadingRelations();
                $products = $productService->getProduct($request, null, null, $with);

                $total = $products->count();
                $message = $total != 1 ? __(':total Products found', compact('total')) : __(':total Product found', compact('total'));

                return $this
                    ->httpResponse()
                    ->setData([
                        'count' => number_format($total),
                        'html' => view(
                            Theme::getThemeNamespace('views.ecommerce.includes.product-items'),
                            ['products' => $products, 'itemsPerRow' => get_products_per_row(), 'layout' => 'grid']
                        )->render(),
                    ])
                    ->setMessage($message);
            });
        }

        // For non-search requests, use longer cache time
        return $cache->remember($cacheKey, Carbon::now()->addHours(2), function () use ($type, $limit) {
            // Original functionality for non-search requests
            $params = [
                'take' => $limit,
            ];

            switch ($type) {
                case 'featured':
                    $products = get_featured_products($params);
                    break;
                case 'on-sale':
                    $products = get_products_on_sale($params);
                    break;
                case 'trending':
                    $products = get_trending_products($params);
                    break;
                case 'top-rated':
                    $products = get_top_rated_products($limit);
                    break;
                case 'all':
                default:
                    $products = get_products($params + app(EcommerceHelper::class)->withReviewsParams());
                    break;
            }

            return $this
                ->httpResponse()
                ->setData([
                    'count' => number_format($products->count()),
                    'html' => view(
                        Theme::getThemeNamespace('views.ecommerce.includes.product-items'),
                        ['products' => $products, 'itemsPerRow' => get_products_per_row(), 'layout' => 'grid']
                    )->render(),
                ]);
        });
    }

    public function ajaxGetCartContent()
    {
        return $this
            ->httpResponse()
            ->setData([
                'content' => Theme::partial('mini-cart.content'),
                'footer' => Theme::partial('mini-cart.footer'),
            ]);
    }

    public function ajaxGetCrossSaleProducts(Product $product, ProductCrossSalePriceService $productCrossSalePriceService)
    {
        $parentProduct = $product;
        $products = $product->crossSaleProducts;

        $productCrossSalePriceService->applyProduct($product);

        return $this
            ->httpResponse()
            ->setData(view(
                Theme::getThemeNamespace(
                    'views.ecommerce.includes.cross-sale-products'
                ),
                compact('products', 'parentProduct')
            )->render());
    }

    public function ajaxGetRelatedProducts(Product $product)
    {
        return $this
            ->httpResponse()
            ->setData(view(
                Theme::getThemeNamespace(
                    'views.ecommerce.includes.related-products'
                ),
                ['products' => get_related_products($product)]
            )->render());
    }
}
